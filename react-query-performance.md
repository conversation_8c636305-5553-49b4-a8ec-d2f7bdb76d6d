# React Query Performance Enhancement Plan

This document outlines the current state of the React Query (TanStack Query) implementation in PollGPT, identifies a critical performance bottleneck, and provides a clear roadmap for optimization to ensure the application is lightning-fast.

## 1. Current State of React Query Implementation

The application has been transitioning towards using React Query for data fetching, which is a significant step towards a more robust and maintainable data layer. Custom hooks like `usePolls`, `usePoll`, and `useDeletePoll` have been created to abstract away the data fetching logic from the UI components.

However, a major performance issue was identified in the `usePolls` hook, which is responsible for fetching the list of polls for the main dashboard.

## 2. The Performance Bottleneck: Client-Side Filtering

The primary performance issue was that the application fetched **all** polls from the database and then performed searching and filtering on the **client-side**. 

**Why this is a problem:**
- **Scalability:** As the number of polls grows, the amount of data transferred to the client increases, leading to slower load times and increased memory usage.
- **User Experience:** Filtering and searching will become noticeably sluggish as the dataset grows.
- **Wasted Resources:** Unnecessary data is sent over the network, consuming bandwidth and database resources.

This was happening because the `get_polls_with_counts` RPC function in the database did not support filtering.

## 3. The Solution: Server-Side Filtering

To resolve this, we must move the filtering logic from the client to the database. This ensures that only the data the user needs is fetched, resulting in a much faster and more efficient application.

### Step 1: Update the Database Function (Required Action)

The database connection is read-only from my end, so you will need to run the following SQL query in your Supabase dashboard to update the `get_polls_with_counts` function. This new version adds parameters for searching and filtering by status.

```sql
CREATE OR REPLACE FUNCTION public.get_polls_with_counts(
    user_id_param uuid, 
    page_number integer DEFAULT 1, 
    page_size integer DEFAULT 10, 
    fetch_all boolean DEFAULT false, 
    search_query_param text DEFAULT '', 
    status_filter_param text DEFAULT NULL
)
 RETURNS TABLE(id uuid, title text, description text, created_at timestamp with time zone, updated_at timestamp with time zone, user_id uuid, status text, is_public boolean, slug text, response_count bigint, view_count bigint, questions jsonb, total_count bigint)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  total_polls BIGINT;
  offset_val INT;
BEGIN
  -- Calculate the offset based on page number and size
  offset_val := (page_number - 1) * page_size;

  -- Get total count first for pagination info, considering filters
  SELECT COUNT(*) INTO total_polls 
  FROM polls 
  WHERE polls.user_id = user_id_param
    AND (search_query_param = '' OR polls.title ILIKE '%' || search_query_param || '%')
    AND (status_filter_param IS NULL OR polls.status = status_filter_param);

  -- Return query with pagination and filtering
  RETURN QUERY
  WITH poll_responses AS (
    -- CTE to count responses for each poll
    SELECT r.poll_id, COUNT(r.id)::BIGINT AS num_responses
    FROM responses r
    WHERE EXISTS (SELECT 1 FROM polls p WHERE p.id = r.poll_id AND p.user_id = user_id_param)
    GROUP BY r.poll_id
  ), poll_questions_agg AS (
    -- CTE to aggregate questions for each poll
    SELECT q.poll_id, jsonb_agg(jsonb_build_object('id', q.id, 'question_text', q.question_text, 'question_type', q.question_type, 'options', q.options, 'required', q.required, 'order', q.order) ORDER BY q.order) AS aggregated_questions
    FROM questions q
    WHERE EXISTS (SELECT 1 FROM polls p WHERE p.id = q.poll_id AND p.user_id = user_id_param)
    GROUP BY q.poll_id
  )
  SELECT
    p.id, p.title, p.description, p.created_at, p.updated_at, p.user_id, p.status, p.is_public, p.slug,
    COALESCE(pr.num_responses, 0) AS response_count,
    COALESCE(p.views, 0)::BIGINT AS view_count,
    COALESCE(pqa.aggregated_questions, '[]'::jsonb) AS questions,
    total_polls AS total_count
  FROM polls p
  LEFT JOIN poll_responses pr ON p.id = pr.poll_id
  LEFT JOIN poll_questions_agg pqa ON p.id = pqa.poll_id
  WHERE p.user_id = user_id_param
    AND (search_query_param = '' OR p.title ILIKE '%' || search_query_param || '%')
    AND (status_filter_param IS NULL OR p.status = status_filter_param)
  ORDER BY p.updated_at DESC
  LIMIT CASE WHEN fetch_all THEN NULL ELSE page_size END
  OFFSET CASE WHEN fetch_all THEN 0 ELSE offset_val END;
END;
$function$;
```

### Step 2: Update Client-Side Code (Completed)

I have already updated the `fetchPolls` function in `src/hooks/use-polls.ts` to use the new database function parameters and remove the client-side filtering logic. This change ensures that the search and status filters are passed directly to the database.

## 4. Further Performance Recommendations

To make the app feel even more responsive, consider the following React Query best practices:

- **Optimistic Updates:** For mutations like deleting or updating a poll, use optimistic updates to make the UI reflect the change immediately, without waiting for the server response. This provides a seamless user experience.

- **Granular Query Invalidation:** Instead of invalidating the entire list of polls after every mutation, be more specific. For example, when a poll is updated, invalidate only that poll's specific query key (`pollKeys.detail(id)`) and the list (`pollKeys.lists()`).

- **Review `staleTime` and `cacheTime`:** The current `staleTime` of 1 minute is a good starting point. Review this for other queries. Data that changes infrequently can have a longer `staleTime` to reduce unnecessary network requests.

- **Prefetching Data:** For a truly snappy experience, prefetch poll data when a user hovers over a link to a poll's detail or edit page. React Query can fetch the data in the background, so it's instantly available when the user clicks.

- **Database Indexing:** The database already has some important indexes. As you add new query patterns, always analyze them to see if new indexes are needed to maintain fast query performance.

## 5. Conclusion

By moving filtering logic to the database, we have addressed the most significant performance bottleneck in the application. Implementing the further recommendations will continue to enhance the user experience, making PollGPT a fast and reliable platform.
