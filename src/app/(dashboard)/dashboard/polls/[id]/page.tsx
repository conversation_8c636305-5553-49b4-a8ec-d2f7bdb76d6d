"use client";

import { useState, useEffect } from "react";
import { renderToStaticMarkup } from "react-dom/server";
import { useParams, useSearchParams } from "next/navigation";
import { Poll, PollQuestion, QuestionOption, QuestionType } from "@/lib/services/polls";

import { usePoll, useUpdatePoll, useDeletePoll } from "@/hooks/use-poll";
import { DragDropContext, Droppable, Draggable, OnDragEndResponder } from "@hello-pangea/dnd";
import {
  BarChartIcon,
  Copy,
  GripVertical,
  Loader2,
  Plus,
  Save,
  Share2,
  Trash2,
  X,
} from "lucide-react";
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";
import { QRCodeSVG } from 'qrcode.react';
import { SimulationRequest, SimulationResponse } from "@/lib/types/simulation";

import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";

const isQuestionOption = (o: unknown): o is QuestionOption => {
  if (typeof o !== 'object' || o === null) return false;
  const opt = o as { id?: unknown; text?: unknown };
  return typeof opt.id === 'string' && typeof opt.text === 'string';
}

// Main component for the poll editing page
export default function PollEditPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const pollId = params.id as string;

  // React Query hooks for data operations
  const { data: fetchedPoll, isLoading, error } = usePoll(pollId);
  const { mutate: updatePollMutation, isPending: isSaving } = useUpdatePoll();
  const { mutate: deletePollMutation } = useDeletePoll();

  // Local state for the poll being edited and UI controls
  const [poll, setPoll] = useState<Poll | null | undefined>(undefined);
  const [showShareDialog, setShowShareDialog] = useState(false);
  const [showSimulationDialog, setShowSimulationDialog] = useState(false);
  const [simulationLoading, setSimulationLoading] = useState(false);
  const [simulationResults, setSimulationResults] = useState<SimulationResponse | null>(null);
  const [selectedDemographic, setSelectedDemographic] = useState("college_students");
  const [sampleSize, setSampleSize] = useState(100);
  const [simulationQuestionIndex, setSimulationQuestionIndex] = useState<number | undefined>(undefined);

  // Sync local state with fetched data

  // Define keys of Poll that handlePollUpdate can modify
  type EditablePollKeys = 'title' | 'description' | 'status' | 'is_public' | 'access_code' | 'expiresAt';

  useEffect(() => {
    if (fetchedPoll) {
      setPoll(fetchedPoll);
      if (searchParams.get('newPoll') === 'true') {
        setShowShareDialog(true);
        const newUrl = window.location.pathname;
        window.history.replaceState({}, '', newUrl);
      }
    }
  }, [fetchedPoll, searchParams]);

  // Overload signatures for handlePollUpdate
  function handlePollUpdate(field: 'title', value: string): void;
  function handlePollUpdate(field: 'description', value: string): void;
  function handlePollUpdate(field: 'status', value: 'draft' | 'active' | 'completed'): void;
  function handlePollUpdate(field: 'is_public', value: boolean): void;
  function handlePollUpdate(field: 'access_code', value: string | undefined): void;
  function handlePollUpdate(field: 'expiresAt', value: string | null): void;
  // Implementation signature
  function handlePollUpdate(field: EditablePollKeys, value: unknown): void {
    if (!poll) return;

    let updatedPollState: Poll | null = null;

    switch (field) {
      case 'title':
        if (typeof value === 'string') {
          updatedPollState = { ...poll, title: value };
        } else {
          console.error(`Invalid type for title: expected string, got ${typeof value}`);
          return;
        }
        break;
      case 'description':
        if (typeof value === 'string') {
          updatedPollState = { ...poll, description: value };
        } else {
          console.error(`Invalid type for description: expected string, got ${typeof value}`);
          return;
        }
        break;
      case 'status':
        if (typeof value === 'string' && ['draft', 'active', 'completed'].includes(value)) {
          updatedPollState = { ...poll, status: value as Poll['status'] };
        } else {
          console.error(`Invalid type for status: expected 'draft'|'active'|'completed', got ${value}`);
          return;
        }
        break;
      case 'is_public':
        if (typeof value === 'boolean') {
          updatedPollState = { ...poll, is_public: value };
        } else {
          console.error(`Invalid type for is_public: expected boolean, got ${typeof value}`);
          return;
        }
        break;
      case 'access_code':
        if (typeof value === 'string' || typeof value === 'undefined') {
          updatedPollState = { ...poll, access_code: value };
        } else {
          console.error(`Invalid type for access_code: expected string or undefined, got ${typeof value}`);
          return;
        }
        break;
      case 'expiresAt':
        if (typeof value === 'string' || value === null) {
          updatedPollState = { ...poll, expiresAt: value as string | null };
        } else {
          console.error(`Invalid type for expiresAt: expected string or null, got ${typeof value}`);
          return;
        }
        break;
      default:
        const _exhaustiveCheck: never = field;
        console.error('Unhandled poll field in handlePollUpdate:', _exhaustiveCheck);
        return;
    }

    if (updatedPollState) {
      setPoll(updatedPollState);
    }
  }


  // Define keys of PollQuestion that handleQuestionUpdate can modify
  type EditableQuestionKeys = 'text' | 'type' | 'required' | 'options';

  // Overload signatures for handleQuestionUpdate
  function handleQuestionUpdate(qIndex: number, field: 'text', value: string): void;
  function handleQuestionUpdate(qIndex: number, field: 'type', value: QuestionType): void;
  function handleQuestionUpdate(qIndex: number, field: 'required', value: boolean): void;
  function handleQuestionUpdate(qIndex: number, field: 'options', value: QuestionOption[]): void;
  // Implementation signature for handleQuestionUpdate
  function handleQuestionUpdate(qIndex: number, field: EditableQuestionKeys, value: unknown): void {
    if (!poll?.questions) return;
    const updatedQuestions = [...poll.questions];
    const questionToUpdate = { ...updatedQuestions[qIndex] };

    let questionUpdated = false;

    switch (field) {
      case 'text':
        if (typeof value === 'string') {
          questionToUpdate.text = value;
          questionUpdated = true;
        } else {
          console.error(`Invalid type for question text: expected string, got ${typeof value}`);
          return;
        }
        break;
      case 'type':
        if (typeof value === 'string' && ['single', 'multiple', 'likert', 'open'].includes(value)) {
          questionToUpdate.type = value as QuestionType;
          questionUpdated = true;
        } else {
          console.error(`Invalid type for question type: expected QuestionType, got ${value}`);
          return;
        }
        break;
      case 'required':
        if (typeof value === 'boolean') {
          questionToUpdate.required = value;
          questionUpdated = true;
        } else {
          console.error(`Invalid type for question required: expected boolean, got ${typeof value}`);
          return;
        }
        break;
      case 'options':
        if (Array.isArray(value) && value.every(isQuestionOption)) {
          questionToUpdate.options = value as QuestionOption[];
          questionUpdated = true;
        } else {
          console.error(`Invalid type for question options: expected QuestionOption[], got ${value}`);
          return;
        }
        break;
      default:
        const _exhaustiveCheck: never = field;
        console.error('Unhandled question field in handleQuestionUpdate:', _exhaustiveCheck);
        return;
    }

    if (questionUpdated) {
      updatedQuestions[qIndex] = questionToUpdate;
      setPoll({ ...poll, questions: updatedQuestions });
    }
  }

  // Save all changes to the poll
  const handleSave = () => {
    if (!poll) return;

    // Create a clean copy of the poll data to avoid any circular references
    const pollToSave = {
      ...poll,
      questions: poll.questions.map(q => ({
        ...q,
        options: q.options ? [...q.options] : undefined
      }))
    };

    console.log('Saving poll:', pollToSave);

    try {
      updatePollMutation(
        { id: poll.id, updates: pollToSave },
        {
          onSuccess: () => {
            console.log('Save successful');
            toast.success("Changes saved successfully");
          },
          onError: (err: Error) => {
            console.error('Save error:', err);
            toast.error(`Failed to save: ${err.message}`);
          },
        }
      );
    } catch (error) {
      console.error('Exception during save:', error);
      toast.error(`Save operation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  };

  // Reorder questions on drag-and-drop
  const handleOnDragEnd: OnDragEndResponder = (result) => {
    if (!result.destination || !poll?.questions) return;

    const items = Array.from(poll.questions);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    const updatedQuestions = items.map((q, index) => ({ ...q, order: index }));
    setPoll({ ...poll, questions: updatedQuestions });
  };

  // Add a new question
  const handleAddQuestion = () => {
    if (!poll) return;
    const newQuestion: PollQuestion = {
      id: `temp-${Date.now()}`,
      text: "New Question",
      type: "multiple",
      required: true,
      order: poll.questions?.length || 0,
      options: [
        { id: `opt1-${Date.now()}`, text: "Option 1", value: "Option 1" },
        { id: `opt2-${Date.now()}`, text: "Option 2", value: "Option 2" },
      ],
    };
    setPoll({ ...poll, questions: [...(poll.questions || []), newQuestion] });
  };

  // Update a specific question's property


  // Delete a question
  const handleDeleteQuestion = (qIndex: number) => {
    if (!poll?.questions) return;
    const updatedQuestions = poll.questions.filter((_, index) => index !== qIndex);
    setPoll({ ...poll, questions: updatedQuestions });
  };

  // Add a new option to a question
  const handleAddOption = (qIndex: number) => {
    if (!poll?.questions) return;
    const question = poll.questions[qIndex];
    const newOption: QuestionOption = {
      id: `temp-opt-${Date.now()}`,
      text: `Option ${ (question.options?.length || 0) + 1}`,
      value: `Option ${ (question.options?.length || 0) + 1}`,
    };
    const updatedOptions = [...(question.options || []), newOption];
    handleQuestionUpdate(qIndex, 'options', updatedOptions);
  };

  // Update a question's option
  const handleOptionUpdate = (qIndex: number, optIndex: number, value: string) => {
    if (!poll?.questions?.[qIndex]?.options) return;
    const updatedOptions = [...poll.questions[qIndex].options!];
    updatedOptions[optIndex] = { ...updatedOptions[optIndex], text: value, value: value };
    handleQuestionUpdate(qIndex, 'options', updatedOptions);
  };

  // Delete a question's option
  const handleDeleteOption = (qIndex: number, optIndex: number) => {
    if (!poll?.questions?.[qIndex]?.options) return;
    if (poll.questions[qIndex].options!.length <= 2) {
      toast.error("A question must have at least 2 options.");
      return;
    }
    const updatedOptions = poll.questions[qIndex].options!.filter((_, i) => i !== optIndex);
    handleQuestionUpdate(qIndex, 'options', updatedOptions);
  };

  const openSimulationDialog = (index?: number) => {
    setSimulationQuestionIndex(index);
    setShowSimulationDialog(true);
  };

  // Run a simulation
  const handleSimulation = async () => {
    if (!poll) return;
    setSimulationLoading(true);
    setSimulationResults(null);

    const question = typeof simulationQuestionIndex === 'number' ? poll.questions?.[simulationQuestionIndex] : null;
    const request: SimulationRequest = {
      pollQuestion: question ? question.text : poll.title,
      pollOptions: question ? question.options?.map(o => o.text) ?? [] : [],
      demographic: {
        group: selectedDemographic.replace(/_/g, ' '),
        size: sampleSize,
        context: poll.title || '',
      },
      responseFormat: 'distribution',
    };

    try {
      const response = await fetch('/api/simulate-poll', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(request),
      });
      if (!response.ok) {
                const errorData: { error?: string } = await response.json();
        throw new Error(errorData.error || 'Simulation failed');
      }
            const apiResult: { simulation: SimulationResponse } = await response.json();
      setSimulationResults(apiResult.simulation);
      toast.success("Simulation completed successfully");
    } catch (err) {
      setSimulationLoading(false);
      const message = err instanceof Error ? err.message : String(err);
      toast.error(`Simulation failed: ${message}`);
    }
  };

  // Delete the entire poll
  const handleDeletePoll = () => {
    if (!poll) return;
    if (window.confirm("Are you sure you want to delete this poll? This cannot be undone.")) {
      deletePollMutation(poll.id, {
        onSuccess: () => {
          toast.success("Poll deleted successfully");
          window.location.href = "/dashboard/polls";
        },
        onError: (err: unknown) => {
          const message = err instanceof Error ? err.message : String(err);
          toast.error(`Failed to delete poll: ${message}`);
        },
      });
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-6 space-y-4">
        <Skeleton className="h-12 w-3/4" />
        <Skeleton className="h-24 w-full" />
        <Skeleton className="h-16 w-full" />
        <Skeleton className="h-16 w-full" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          <p>Error loading poll: {error.message}</p>
        </div>
      </div>
    );
  }

  if (!poll) {
    return (
      <div className="container mx-auto py-6">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded">
          <p>Poll not found.</p>
        </div>
      </div>
    );
  }

  // Main poll editor UI
  return (
    <div className="container mx-auto py-6">
      {/* Back button */}
      <div className="mb-4">
        <Button
          variant="outline"
          onClick={() => window.location.href = "/dashboard/polls"}
          className="flex items-center gap-2"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M19 12H5M12 19l-7-7 7-7"/>
          </svg>
          Back to Polls
        </Button>
      </div>
      {/* Header */}
      <div className="flex flex-wrap justify-between items-start gap-4 mb-6">
        <div>
          <h1 className="text-3xl font-bold">{poll.title || 'Untitled Poll'}</h1>
          <p className="text-muted-foreground text-sm mt-1">
            {poll.status} • Last updated: {poll.updatedAt && new Date(poll.updatedAt).getTime() > 0 ? new Date(poll.updatedAt).toLocaleString() : 'Just now'}
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleSave} disabled={isSaving} className="bg-primary text-primary-foreground hover:bg-primary/90">
            {isSaving ? <><Loader2 className="w-4 h-4 mr-2 animate-spin" /> Saving...</> : <><Save className="w-4 h-4 mr-2"/>Save</>}
          </Button>
          <Button variant="outline" onClick={() => setShowShareDialog(true)}><Share2 className="w-4 h-4 mr-2" /> Share</Button>
          <Button onClick={() => openSimulationDialog()} variant="outline" className="ml-2">
            <BarChartIcon className="mr-2 h-4 w-4" />
            Simulate
          </Button>
          <Button variant="destructive" onClick={handleDeletePoll}><Trash2 className="w-4 h-4 mr-2" /> Delete</Button>
        </div>
      </div>

      {/* Poll Details Card */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Poll Details</CardTitle>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="title">Title</Label>
            <Input id="title" value={poll.title || ''} onChange={(e) => handlePollUpdate('title', e.target.value)} />
          </div>
          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea id="description" value={poll.description || ''} onChange={(e) => handlePollUpdate('description', e.target.value)} />
          </div>
          <div>
            <Label htmlFor="status">Status</Label>
            <Select value={poll.status} onValueChange={(value) => handlePollUpdate('status', value as 'draft' | 'active' | 'completed')}>
              <SelectTrigger><SelectValue /></SelectTrigger>
              <SelectContent>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="is_public" checked={poll.is_public} onCheckedChange={(checked) => handlePollUpdate("is_public", Boolean(checked))} />
            <Label htmlFor="is_public">Make this poll public</Label>
          </div>
        </CardContent>
      </Card>

      {/* Questions Card */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Questions</CardTitle>
          <CardDescription>Drag and drop to reorder questions.</CardDescription>
        </CardHeader>
        <CardContent>
          <DragDropContext onDragEnd={handleOnDragEnd}>
            <Droppable droppableId="questions">
              {(provided) => (
                <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-4">
                  {poll.questions?.map((question, index) => (
                    <Draggable key={question.id} draggableId={question.id} index={index}>
                      {(provided) => (
                        <div ref={provided.innerRef} {...provided.draggableProps} className="border rounded-md p-4 bg-background">
                          <div className="flex flex-col md:flex-row md:justify-between md:items-start mb-2 gap-2">
                            <div className="flex items-center gap-2">
                              <div {...provided.dragHandleProps} className="cursor-move"><GripVertical className="w-5 h-5 text-gray-400" /></div>
                              <div className="text-sm font-medium text-muted-foreground">Question {index + 1}</div>
                            </div>
                            <div className="flex items-center gap-2 self-end md:self-auto">
                              <Button onClick={() => openSimulationDialog(index)} size="sm" variant="outline" className="border-blue-200 bg-blue-50 hover:bg-blue-100 text-blue-700">
                                <BarChartIcon className="mr-2 h-4 w-4" />
                                Simulate
                              </Button>
                              <Button variant="ghost" size="sm" onClick={() => handleDeleteQuestion(index)}><Trash2 className="w-4 h-4 text-red-500" /></Button>
                            </div>
                          </div>
                          <div className="space-y-3 mt-3 pl-6">
                            <div>
                              <Label htmlFor={`q-text-${index}`}>Question Text</Label>
                              <Input id={`q-text-${index}`} value={question.text} onChange={(e) => handleQuestionUpdate(index, 'text', e.target.value)} />
                            </div>
                            <div>
                              <Label htmlFor={`q-type-${index}`}>Question Type</Label>
                              <Select value={question.type} onValueChange={(value) => handleQuestionUpdate(index, 'type', value as QuestionType)}>
                                <SelectTrigger><SelectValue /></SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="single">Single Choice</SelectItem>
                                  <SelectItem value="multiple">Multiple Choice</SelectItem>
                                  <SelectItem value="likert">Likert Scale</SelectItem>
                                  <SelectItem value="open">Open-ended</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                            {question.type !== 'open' && (
                              <div>
                                <Label>Options</Label>
                                <div className="space-y-2 mt-1">
                                  {question.options?.map((option, optIndex) => (
                                    <div key={option.id || optIndex} className="flex items-center gap-2">
                                      <Input value={option.text} onChange={(e) => handleOptionUpdate(index, optIndex, e.target.value)} />
                                      <Button variant="ghost" size="sm" onClick={() => handleDeleteOption(index, optIndex)} disabled={question.options!.length <= 2}><X className="w-4 h-4" /></Button>
                                    </div>
                                  ))}
                                  <Button variant="outline" size="sm" onClick={() => handleAddOption(index)} className="w-full mt-2"><Plus className="w-4 h-4 mr-2" /> Add Option</Button>
                                </div>
                              </div>
                            )}
                            <div className="flex items-center space-x-2 mt-2">
                              <Checkbox id={`q-req-${index}`} checked={question.required} onCheckedChange={(checked) => handleQuestionUpdate(index, 'required', !!checked)} />
                              <Label htmlFor={`q-req-${index}`}>Required</Label>
                            </div>
                          </div>
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
          <Button onClick={handleAddQuestion} className="w-full mt-4"><Plus className="w-4 h-4 mr-2" /> Add Question</Button>
        </CardContent>
      </Card>

      {/* Floating Save Button */}
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          onClick={handleSave}
          disabled={isSaving}
          className="min-w-[120px] shadow-lg bg-primary text-primary-foreground hover:bg-primary/90"
          size="lg"
        >
          {isSaving ? <><Loader2 className="w-4 h-4 mr-2 animate-spin" /> Saving...</> : <><Save className="w-4 h-4 mr-2"/>Save Changes</>}
        </Button>
      </div>

      {/* Share Dialog */}
      <Dialog open={showShareDialog} onOpenChange={setShowShareDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Share your poll</DialogTitle>
            <DialogDescription>
              Share your poll with others using these options.
            </DialogDescription>
          </DialogHeader>
          <Tabs defaultValue="link" className="mt-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="link">Link</TabsTrigger>
              <TabsTrigger value="qrcode">QR Code</TabsTrigger>
              <TabsTrigger value="embed">Embed</TabsTrigger>
            </TabsList>
            <TabsContent value="link" className="mt-4">
              <p className="text-sm text-gray-500 mb-2">Anyone with the link can view and respond.</p>
              <div className="flex items-center space-x-2">
                <Input value={`${window.location.origin}/p/${poll.slug || poll.id}`} readOnly />
                <Button onClick={() => { navigator.clipboard.writeText(`${window.location.origin}/p/${poll.slug || poll.id}`); toast.success("Link copied!"); }}><Copy className="w-4 h-4" /></Button>
              </div>
            </TabsContent>
            <TabsContent value="qrcode" className="mt-4 flex flex-col items-center justify-center">
                <div className="p-4 bg-white rounded-md">
                    <QRCodeSVG value={`${window.location.origin}/p/${poll.slug || poll.id}`} size={160} />
                </div>
                <a
                  href={`data:image/svg+xml;utf8,${encodeURIComponent(renderToStaticMarkup(<QRCodeSVG value={`${window.location.origin}/p/${poll.slug || poll.id}`} />))}`}
                  download="qrcode.svg"
                  className="text-sm mt-2 text-blue-500 hover:underline"
                >
                    Download QR Code
                </a>
            </TabsContent>
            <TabsContent value="embed" className="mt-4">
                <p className="text-sm text-gray-500 mb-2">Embed this poll on your website.</p>
                <Textarea
                    readOnly
                    value={`<iframe src="${window.location.origin}/p/${poll.slug || poll.id}/embed" style="width: 100%; height: 600px; border: none;" title="Poll: ${poll.title}"></iframe>`}
                    className="h-24"
                />
                <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => { navigator.clipboard.writeText(`<iframe src="${window.location.origin}/p/${poll.slug || poll.id}/embed" style="width: 100%; height: 600px; border: none;" title="Poll: ${poll.title}"></iframe>`); toast.success("Embed code copied!"); }}>
                    <Copy className="w-4 h-4 mr-2" /> Copy Embed Code
                </Button>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>

      {/* Simulation Dialog */}
      <Dialog open={showSimulationDialog} onOpenChange={(isOpen) => {
        setShowSimulationDialog(isOpen);
        if (!isOpen) {
          setSimulationResults(null);
          setSimulationQuestionIndex(undefined);
        }
      }}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Simulate Poll Responses</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Simulate how a specific demographic might respond to this poll or a specific question.
            </p>
            <div>
              <Label htmlFor="demographic">Target Demographic</Label>
              <Select value={selectedDemographic} onValueChange={setSelectedDemographic}>
                <SelectTrigger id="demographic">
                  <SelectValue placeholder="Select demographic" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="general_population">General Population</SelectItem>
                  <SelectItem value="college_students">College Students</SelectItem>
                  <SelectItem value="tech_professionals">Tech Professionals</SelectItem>
                  <SelectItem value="gamers">Gamers</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="sampleSize">Sample Size</Label>
              <Input
                id="sampleSize"
                type="number"
                value={sampleSize}
                onChange={(e) => setSampleSize(parseInt(e.target.value, 10))}
              />
            </div>
            <Button onClick={handleSimulation} disabled={simulationLoading} className="w-full">
              {simulationLoading ? (
                <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Simulating...</>
              ) : (
                'Run Simulation'
              )}
            </Button>
            {simulationResults && simulationResults.results && (
              <div className="mt-4 p-4 bg-muted/50 rounded-lg">
                <h3 className="font-bold text-lg mb-2">Simulation Results</h3>
                {simulationResults.results.distribution && (
                  <ul className="space-y-2">
                    {Object.entries(simulationResults.results.distribution).map(([option, percentage]) => {
                      // Safely convert percentage to number and format it
                      const formattedPercentage = typeof percentage === 'number'
                        ? percentage.toFixed(1)
                        : parseFloat(String(percentage) || '0').toFixed(1);

                      return (
                        <li key={option} className="flex justify-between items-center">
                          <span>{option}</span>
                          <span className="font-semibold">{formattedPercentage}%</span>
                        </li>
                      );
                    })}
                  </ul>
                )}
                {simulationResults.results.analysis && (
                  <div className="mt-4">
                    <h4 className="font-semibold">Analysis</h4>
                    <p className="text-sm text-muted-foreground mt-1">{simulationResults.results.analysis}</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>


    </div>
  );
}
