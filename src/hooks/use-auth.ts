import { 
  useQuery, 
  useMutation, 
  useQueryClient 
} from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { User } from '@supabase/supabase-js';

// Query keys for React Query
export const authKeys = {
  all: ['auth'] as const,
  user: () => [...authKeys.all, 'user'] as const,
  profile: () => [...authKeys.all, 'profile'] as const,
  profileById: (id: string) => [...authKeys.profile(), id] as const,
};

// Interface for user profile
export interface UserProfile {
  id: string;
  email: string | undefined;
  name: string | null;
  avatar_url: string | null;
  created_at: string | null;
}

/**
 * Custom hook for fetching the current authenticated user with caching
 */
export function useUser() {
  return useQuery({
    queryKey: authKeys.user(),
    queryFn: async (): Promise<User | null> => {
      const { data: { user }, error } = await supabase.auth.getUser();
      
      if (error) {
        console.error('Error fetching user:', error);
        throw new Error(error.message);
      }
      
      return user;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    retry: 1,
  });
}

/**
 * Custom hook for fetching the current user's profile with caching
 */
export function useUserProfile() {
  const { data: user } = useUser();
  
  return useQuery({
    queryKey: authKeys.profileById(user?.id || 'unauthorized'),
    queryFn: async (): Promise<UserProfile | null> => {
      if (!user?.id) return null;
      
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();
      
      if (error) {
        console.error('Error fetching profile:', error);
        throw new Error(error.message);
      }
      
      return {
        id: data.id,
        email: user.email,
        name: data.name,
        avatar_url: data.avatar_url,
        created_at: data.created_at,
      };
    },
    enabled: !!user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Custom hook for signing in with email and password
 */
export function useSignIn() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      email, 
      password 
    }: { 
      email: string; 
      password: string 
    }) => {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      
      if (error) {
        throw new Error(error.message);
      }
      
      return data;
    },
    onSuccess: () => {
      // Invalidate and refetch user data
      queryClient.invalidateQueries({ queryKey: authKeys.user() });
    },
  });
}

/**
 * Custom hook for signing up with email and password
 */
export function useSignUp() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      email, 
      password, 
      name 
    }: { 
      email: string; 
      password: string;
      name: string;
    }) => {
      // Sign up user
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
      });
      
      if (authError) {
        throw new Error(authError.message);
      }
      
      if (authData.user) {
        // Create profile
        const { error: profileError } = await supabase
          .from('profiles')
          .insert({
            id: authData.user.id,
            email: email,
            name: name,
          });
        
        if (profileError) {
          throw new Error(profileError.message);
        }
      }
      
      return authData;
    },
    onSuccess: () => {
      // Invalidate and refetch user data
      queryClient.invalidateQueries({ queryKey: authKeys.user() });
    },
  });
}

/**
 * Custom hook for signing out
 */
export function useSignOut() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async () => {
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        throw new Error(error.message);
      }
      
      return true;
    },
    onSuccess: () => {
      // Clear user data from cache
      queryClient.invalidateQueries({ queryKey: authKeys.all });
    },
  });
}

/**
 * Custom hook for updating user profile
 */
export function useUpdateProfile() {
  const queryClient = useQueryClient();
  const { data: user } = useUser();
  
  return useMutation({
    mutationFn: async (profile: Partial<UserProfile>) => {
      if (!user?.id) {
        throw new Error('User not authenticated');
      }
      
      const { data, error } = await supabase
        .from('profiles')
        .update(profile)
        .eq('id', user.id);
      
      if (error) {
        throw new Error(error.message);
      }
      
      return data;
    },
    onSuccess: () => {
      if (user?.id) {
        // Update the cache with the new profile data
        queryClient.invalidateQueries({ 
          queryKey: authKeys.profileById(user.id) 
        });
      }
    },
  });
}
