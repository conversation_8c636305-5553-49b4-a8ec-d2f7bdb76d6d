import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { Session } from '@supabase/supabase-js';
import { useSession, useRefreshSession } from '@/hooks/use-session';
import { useRouter } from 'next/navigation';

// Context type definitions
interface SessionContextType {
  session: Session | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  refreshSession: () => void;
}

// Create context with default values
const SessionContext = createContext<SessionContextType>({
  session: null,
  isAuthenticated: false,
  isLoading: true,
  refreshSession: () => {}
});

// Custom hook for using the session context
export const useSessionContext = () => useContext(SessionContext);

interface SessionProviderProps {
  children: ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
  refreshInterval?: number;
}

export function SessionProvider({
  children,
  requireAuth = false,
  redirectTo = '/auth/signin',
  refreshInterval = 5 * 60 * 1000 // Default 5 minutes
}: SessionProviderProps) {
  // Use our React Query hooks for session management
  const { data: session, isLoading, isError } = useSession();
  const { mutate: refreshSession } = useRefreshSession();
  const router = useRouter();
  const [isInitializing, setIsInitializing] = useState(true);
  
  // Check authentication and redirect if needed
  useEffect(() => {
    // Skip during initial SSR
    if (typeof window === 'undefined') return;
    
    // Wait for session loading to complete
    if (isLoading) return;
    
    // If initialization is complete and auth is required but no session,
    // redirect to signin page
    if (!isInitializing && requireAuth && !session) {
      router.push(redirectTo);
    }
    
    // Mark initialization as complete after loading
    if (!isLoading && isInitializing) {
      setIsInitializing(false);
    }
  }, [isLoading, session, requireAuth, redirectTo, router, isInitializing]);
  
  // Set up automatic session refresh
  useEffect(() => {
    if (!session) return;
    
    // Calculate time until session expiration
    const expiresAt = new Date(session.expires_at ? session.expires_at * 1000 : 0);
    const timeUntilExpiry = expiresAt.getTime() - Date.now();
    
    // If session is about to expire, refresh it immediately
    if (timeUntilExpiry < refreshInterval) {
      refreshSession();
      return;
    }
    
    // Set up a timer to refresh before expiration
    const timer = setTimeout(() => {
      refreshSession();
    }, timeUntilExpiry - refreshInterval);
    
    return () => clearTimeout(timer);
  }, [session, refreshSession, refreshInterval]);
  
  // Set up periodic refresh regardless of expiry
  useEffect(() => {
    if (!session) return;
    
    // Set up a periodic refresh timer
    const timer = setInterval(() => {
      refreshSession();
    }, refreshInterval);
    
    return () => clearInterval(timer);
  }, [session, refreshSession, refreshInterval]);
  
  // Handle session errors by refreshing
  useEffect(() => {
    if (isError) {
      refreshSession();
    }
  }, [isError, refreshSession]);
  
  // Provide context values
  const value = {
    session,
    isAuthenticated: !!session,
    isLoading: isLoading || isInitializing,
    refreshSession: () => refreshSession()
  };
  
  return (
    <SessionContext.Provider value={value}>
      {children}
    </SessionContext.Provider>
  );
}
