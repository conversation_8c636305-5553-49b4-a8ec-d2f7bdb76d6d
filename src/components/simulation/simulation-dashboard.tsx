"use client";

import { useState, useEffect } from "react";
import { Database } from "@/lib/database.types";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  ArrowLeft,
  BarChart3,
  Brain,
  ChevronRight,
  Download,
  ListChecks,
  MessageSquareText,
  Share2,
  Users
} from "lucide-react";
import Link from "next/link";
import { Separator } from "@/components/ui/separator";
import { SimulationSetup } from "./simulation-setup";
import { SimulationResults } from "./simulation-results";
import { useSimulation } from "@/lib/hooks/use-simulation";

type Poll = Database["public"]["Tables"]["polls"]["Row"] & {
  questions: Database["public"]["Tables"]["questions"]["Row"][]
};

interface SimulationDashboardProps {
  poll: Poll;
}

export default function SimulationDashboard({ poll }: SimulationDashboardProps) {
  // Generate a unique key for this poll's simulation state
  const stateKey = `simulation_state_${poll.id}`;
  
  // Initialize state from localStorage if available
  const [activeQuestion, setActiveQuestion] = useState<number>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem(stateKey);
      if (saved) {
        try {
          const parsed = JSON.parse(saved);
          return parsed.activeQuestion || 0;
        } catch (e) {
          console.error('Error parsing saved simulation state:', e);
        }
      }
    }
    return 0;
  });
  
  const [activeTab, setActiveTab] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem(stateKey);
      if (saved) {
        try {
          const parsed = JSON.parse(saved);
          return parsed.activeTab || 'setup';
        } catch (e) {
          console.error('Error parsing saved simulation state:', e);
        }
      }
    }
    return 'setup';
  });

  const {
    simulation,
    isLoading,
    error,
    demographic,
    setDemographic,
    sampleSize,
    setSampleSize,
    specialInstructions,
    setSpecialInstructions,
    startSimulation,
    resetSimulation
  } = useSimulation(poll.id);
  
  // Save state to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const stateToSave = {
        activeQuestion,
        activeTab,
        timestamp: Date.now()
      };
      localStorage.setItem(stateKey, JSON.stringify(stateToSave));
    }
  }, [activeQuestion, activeTab, stateKey]);

  const currentQuestion = poll.questions[activeQuestion];
  const hasNextQuestion = activeQuestion < poll.questions.length - 1;
  const hasPrevQuestion = activeQuestion > 0;
  const isTextQuestion = currentQuestion?.question_type === "text";

  const handleNextQuestion = () => {
    if (hasNextQuestion) {
      setActiveQuestion(prev => prev + 1);
    }
  };

  const handlePrevQuestion = () => {
    if (hasPrevQuestion) {
      setActiveQuestion(prev => prev - 1);
    }
  };

  const handleStartSimulation = async () => {
    const questionOptions =
      Array.isArray(currentQuestion.options)
        ? currentQuestion.options.map(opt => {
            if (typeof opt === "string") return opt;
            if (opt && typeof opt === "object" && "text" in opt && typeof opt.text === "string") return opt.text;
            return "";
          })
        : [];
    // Create a summary of the poll for context
    const pollContext = `Poll Title: ${poll.title}\nPoll Description: ${poll.description || 'No description'}\nTotal Questions: ${poll.questions.length}\nCurrent Question: ${activeQuestion + 1} of ${poll.questions.length}`;
    
    await startSimulation({
      pollId: poll.id,
      questionId: currentQuestion.id,
      pollQuestion: currentQuestion.question_text,
      pollOptions: questionOptions || [],
      demographic: {
        group: demographic,
        size: sampleSize,
        context: "", // Keep this empty as we'll use dedicated fields
      },
      responseFormat: isTextQuestion ? "individual" : "distribution",
      questionType: isTextQuestion ? "open_ended" : "multiple_choice",
      specialInstructions: specialInstructions,
      pollContext: pollContext
    });

    setActiveTab("results");
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Link href={`/dashboard/polls/${poll.id}`} passHref>
              <Button variant="ghost" size="sm" className="gap-1">
                <ArrowLeft className="h-4 w-4" />
                Back to Poll
              </Button>
            </Link>
            <h1 className="text-2xl font-bold">{poll.title}</h1>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="gap-1">
              <Download className="h-4 w-4" />
              Export
            </Button>
            <Button variant="outline" size="sm" className="gap-1">
              <Share2 className="h-4 w-4" />
              Share
            </Button>
          </div>
        </div>
        <p className="text-muted-foreground">{poll.description || "No description provided."}</p>
      </div>

      <Separator />

      {/* Questions Navigation */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <ListChecks className="h-5 w-5" />
          Question {activeQuestion + 1} of {poll.questions.length}
        </h2>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handlePrevQuestion}
            disabled={!hasPrevQuestion}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleNextQuestion}
            disabled={!hasNextQuestion}
          >
            Next
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
      </div>

      {/* Current Question */}
      <div className="bg-muted/40 p-4 rounded-lg border">
        <h3 className="font-medium text-lg mb-2">
          {currentQuestion?.question_text || "No question selected"}
        </h3>
        {!isTextQuestion ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 mt-3">
            {(
              (currentQuestion?.options as {id: string, text: string, value: string}[] | string[] | null) || []
            ).map((option, index) => (
              <div
                key={typeof option === 'string' ? index : option.id || index}
                className="bg-background flex items-center border rounded-md px-3 py-2"
              >
                {typeof option === "string" ? option : option.text}
              </div>
            ))}
          </div>
        ) : (
          <div className="flex items-center mt-2 gap-2 text-sm text-muted-foreground">
            <MessageSquareText className="h-4 w-4" />
            <span>Open-ended question (text responses)</span>
          </div>
        )}
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mt-6">
        <TabsList className="grid grid-cols-2 w-full max-w-md">
          <TabsTrigger value="setup" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Simulation Setup
          </TabsTrigger>
          <TabsTrigger
            value="results"
            disabled={!simulation}
            className="flex items-center gap-2"
          >
            <BarChart3 className="h-4 w-4" />
            Results
          </TabsTrigger>
        </TabsList>

        <TabsContent value="setup" className="mt-6">
          <SimulationSetup
            isLoading={isLoading}
            demographic={demographic}
            setDemographic={setDemographic}
            sampleSize={sampleSize}
            setSampleSize={setSampleSize}
            specialInstructions={specialInstructions}
            setSpecialInstructions={setSpecialInstructions}
            onSimulate={handleStartSimulation}
            error={error}
            questionType={isTextQuestion ? "open_ended" : "multiple_choice"}
          />
        </TabsContent>

        <TabsContent value="results" className="mt-6">
          {simulation ? (
            <SimulationResults
              results={simulation}
              enhancedUI={true}
              options={!isTextQuestion ?
                (currentQuestion?.options as string[] || []) :
                undefined}
            />
          ) : (
            <div className="text-center py-12">
              <Brain className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-xl font-medium mb-2">No simulation results yet</h3>
              <p className="text-muted-foreground mb-6">
                Configure your simulation parameters and run the simulation to see results
              </p>
              <Button onClick={() => setActiveTab("setup")}>
                Configure Simulation
              </Button>
            </div>
          )}

          {simulation && (
            <div className="flex items-center justify-end gap-3 mt-8">
              <Button
                variant="outline"
                onClick={resetSimulation}
              >
                New Simulation
              </Button>
              <Button
                onClick={handleNextQuestion}
                disabled={!hasNextQuestion}
              >
                Simulate Next Question
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
