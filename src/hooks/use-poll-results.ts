import { useQuery, useMutation } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { ProcessedPollData } from '@/lib/types/poll';

// Query keys for React Query
export const pollResultsKeys = {
  all: ['pollResults'] as const,
  lists: () => [...pollResultsKeys.all, 'list'] as const,
  list: (filters: Record<string, unknown>) => [...pollResultsKeys.lists(), filters] as const,
  details: () => [...pollResultsKeys.all, 'detail'] as const,
  detail: (id: string) => [...pollResultsKeys.details(), id] as const,
};

/**
 * Hook for fetching processed poll results data
 * @param pollId The ID of the poll to fetch results for
 */
export function usePollResults(pollId: string) {
  return useQuery<ProcessedPollData, Error>({
    queryKey: pollResultsKeys.detail(pollId),
    queryFn: async () => {
      // Get the current user session
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('Not authenticated');
      }
      
      // Fetch poll details
      const { data: pollData, error: pollError } = await supabase
        .from('polls')
        .select(`
          id,
          title,
          description,
          created_at,
          views
        `)
        .eq('id', pollId)
        .single();
      
      if (pollError) {
        console.error('Error fetching poll:', pollError);
        throw new Error(pollError.message);
      }
      
      // Fetch questions
      const { data: questions, error: questionsError } = await supabase
        .from('questions')
        .select('*')
        .eq('poll_id', pollId)
        .order('order');
      
      if (questionsError) {
        console.error('Error fetching questions:', questionsError);
        throw new Error(questionsError.message);
      }
      
      // Fetch responses
      const { data: responses, error: responsesError } = await supabase
        .from('responses')
        .select(`
          id,
          created_at,
          respondent_info,
          answers (
            id,
            question_id,
            answer_value
          )
        `)
        .eq('poll_id', pollId);
      
      if (responsesError) {
        console.error('Error fetching responses:', responsesError);
        throw new Error(responsesError.message);
      }
      
      // Process the responses into a format suitable for charts and analysis
      const processedQuestions = questions.map(q => {
        // Get all answers for this question
        const questionAnswers = responses.flatMap(r => 
          r.answers.filter((a: { question_id: string }) => a.question_id === q.id)
        );
        
        // Process answers based on question type
        let results;
        if (q.question_type === 'open') {
          // For open questions, just list the responses
          results = questionAnswers.map((answer: { answer_value: string }, index: number) => ({
            id: index,
            response: answer.answer_value
          }));
        } else {
          // For option-based questions, count occurrences of each option
          const counts: Record<string, number> = {};
          
          questionAnswers.forEach((answer: { answer_value: string | string[] }) => {
            const value = Array.isArray(answer.answer_value) 
              ? answer.answer_value 
              : [answer.answer_value];
              
            value.forEach((v: string) => {
              counts[v] = (counts[v] || 0) + 1;
            });
          });
          
          // Convert to chart data format
          results = Object.entries(counts).map(([name, value]) => {
            // Find the display text for this option value
            const option = q.options?.find((opt: { value: string; text: string }) => opt.value === name);
            return {
              name: option?.text || name,
              value
            };
          });
        }
        
        return {
          id: q.id,
          text: q.question_text,
          type: q.question_type,
          options: q.options,
          results
        };
      });
      
      // Process response dates for trend analysis
      const responsesByDate = processResponseDates(responses);
      
      // Process demographic data
      const demographics = processDemographics(responses);
      
      // Calculate completion metrics
      const completedResponses = responses.filter(r => 
        r.answers && r.answers.length >= questions.length
      ).length;
      
      const partialResponses = responses.length - completedResponses;
      
      // Calculate average time to complete (if available in respondent_info)
      let totalTime = 0;
      let timeDataPoints = 0;
      
      responses.forEach(r => {
        if (r.respondent_info?.timeToComplete) {
          totalTime += Number(r.respondent_info.timeToComplete);
          timeDataPoints++;
        }
      });
      
      const averageTimeToComplete = timeDataPoints > 0 
        ? `${Math.round(totalTime / timeDataPoints)}s`
        : 'N/A';
      
      // Calculate completion rate
      const completionRate = responses.length > 0
        ? `${Math.round((completedResponses / responses.length) * 100)}%`
        : '0%';
      
      return {
        id: pollData.id,
        title: pollData.title,
        description: pollData.description || '',
        createdAt: pollData.created_at,
        completedResponses,
        partialResponses,
        totalViews: pollData.views || 0,
        completionRate,
        averageTimeToComplete,
        questions: processedQuestions,
        responsesByDate,
        demographics
      };
    },
    staleTime: 60 * 1000, // 1 minute
  });
}

interface Response {
  created_at: string;
  answers: Array<{ question_id: string; answer_value: string | string[] }>;
  respondent_info?: {
    timeToComplete?: number;
    deviceType?: string;
    browser?: string;
    os?: string;
    region?: string;
    [key: string]: unknown;
  };
}

/**
 * Process response dates for trend analysis
 */
function processResponseDates(responses: Response[]): { date: string; count: number }[] {
  // Group responses by date
  const dateGroups: Record<string, number> = {};
  
  responses.forEach(response => {
    const date = new Date(response.created_at).toISOString().split('T')[0];
    dateGroups[date] = (dateGroups[date] || 0) + 1;
  });
  
  // Convert to array format
  return Object.entries(dateGroups)
    .map(([date, count]) => ({ date, count }))
    .sort((a, b) => a.date.localeCompare(b.date));
}

/**
 * Process demographic data from responses
 */
function processDemographics(responses: Response[]): { 
  devices: { name: string; value: number }[]; 
  regions: { name: string; value: number }[]; 
} {
  // Count device types
  const deviceCounts: Record<string, number> = {};
  const regionCounts: Record<string, number> = {};
  
  responses.forEach(response => {
    if (response.respondent_info) {
      // Process device type
      const deviceType = response.respondent_info.deviceType || 'unknown';
      deviceCounts[deviceType] = (deviceCounts[deviceType] || 0) + 1;
      
      // Process region
      const region = response.respondent_info.region || 'unknown';
      regionCounts[region] = (regionCounts[region] || 0) + 1;
    }
  });
  
  // Convert to chart data format
  const devices = Object.entries(deviceCounts).map(([name, value]) => ({ name, value }));
  const regions = Object.entries(regionCounts).map(([name, value]) => ({ name, value }));
  
  return { devices, regions };
}

/**
 * Hook for exporting poll results data
 */
export function useExportPollResults() {
  return useMutation({
    mutationFn: async ({ pollId, format }: { pollId: string; format: 'csv' | 'json' }) => {
      const response = await fetch(`/api/polls/${pollId}/export?format=${format}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to export poll results');
      }
      
      if (format === 'csv') {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `poll-results-${pollId}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        return { success: true };
      }
      
      return response.json();
    },
  });
}
