import { useState, useEffect } from 'react';
import { SimulationResponse } from '../types/simulation';

interface SimulationOptions {
  pollId?: string;
  questionId?: string;
  pollQuestion: string;
  pollOptions: string[];
  demographic: {
    group: string;
    size: number;
    context?: string;
  };
  responseFormat: 'distribution' | 'individual' | 'both';
  questionType: 'multiple_choice' | 'open_ended';
  specialInstructions?: string;
  pollContext?: string;
}

export function useSimulation(pollId?: string) {
  // Create a unique key for storing simulation data
  const storageKey = pollId ? `simulation_data_${pollId}` : 'simulation_data_temp';
  
  // Initialize state from localStorage if available
  const [demographic, setDemographic] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem(storageKey);
      if (saved) {
        try {
          const parsed = JSON.parse(saved);
          return parsed.demographic || 'college_students';
        } catch (e) {
          console.error('Error parsing saved simulation data:', e);
        }
      }
    }
    return 'college_students';
  });
  
  const [sampleSize, setSampleSize] = useState<number>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem(storageKey);
      if (saved) {
        try {
          const parsed = JSON.parse(saved);
          return parsed.sampleSize || 100;
        } catch (e) {
          console.error('Error parsing saved simulation data:', e);
        }
      }
    }
    return 100;
  });
  
  const [specialInstructions, setSpecialInstructions] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem(storageKey);
      if (saved) {
        try {
          const parsed = JSON.parse(saved);
          return parsed.specialInstructions || '';
        } catch (e) {
          console.error('Error parsing saved simulation data:', e);
        }
      }
    }
    return '';
  });
  
  const [simulation, setSimulation] = useState<SimulationResponse | null>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem(storageKey);
      if (saved) {
        try {
          const parsed = JSON.parse(saved);
          return parsed.simulation || null;
        } catch (e) {
          console.error('Error parsing saved simulation data:', e);
        }
      }
    }
    return null;
  });
  
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | undefined>(undefined);
  
  // Save state to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const stateToSave = {
        demographic,
        sampleSize,
        specialInstructions,
        simulation,
        timestamp: Date.now()
      };
      localStorage.setItem(storageKey, JSON.stringify(stateToSave));
    }
  }, [demographic, sampleSize, specialInstructions, simulation, storageKey]);

  const startSimulation = async (options: SimulationOptions) => {
    setIsLoading(true);
    setError("");
    
    try {
      const response = await fetch("/api/simulate-poll", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          pollId: options.pollId,
          questionId: options.questionId,
          pollQuestion: options.pollQuestion,
          pollOptions: options.pollOptions || [],
          demographic: {
            group: options.demographic.group,
            size: options.demographic.size,
            context: options.demographic.context || "",
          },
          responseFormat: options.responseFormat,
          questionType: options.questionType,
          specialInstructions: options.specialInstructions,
          pollContext: options.pollContext,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to simulate poll');
      }

      const data = await response.json();
      // Extract the simulation data from the API response
      if (data.success && data.simulation) {
        setSimulation(data.simulation);
      } else {
        throw new Error('Invalid response format from simulation API');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const resetSimulation = () => {
    setSimulation(null);
    // Also clear from localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem(storageKey);
    }
  };

  return {
    demographic,
    setDemographic,
    sampleSize,
    setSampleSize,
    specialInstructions,
    setSpecialInstructions,
    simulation,
    isLoading,
    error,
    startSimulation,
    resetSimulation,
  };
}
