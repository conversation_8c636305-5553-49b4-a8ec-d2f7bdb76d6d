import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Brain, AlertCircle, Refresh<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>riangle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface SimulationSetupProps {
  isLoading: boolean;
  demographic: string;
  setDemographic: (demographic: string) => void;
  sampleSize: number;
  setSampleSize: (size: number) => void;
  specialInstructions: string;
  setSpecialInstructions: (instructions: string) => void;
  onSimulate: () => void;
  error?: string;
  questionType: "multiple_choice" | "open_ended";
}

// Predefined demographic groups
const demographicOptions = [
  { value: "college_students", label: "College Students" },
  { value: "working_professionals", label: "Working Professionals" },
  { value: "high_school_students", label: "High School Students" },
  { value: "parents", label: "Parents" },
  { value: "seniors", label: "Seniors (65+)" },
  { value: "gen_z", label: "Gen Z (18-25)" },
  { value: "millennials", label: "Millennials (26-41)" },
  { value: "gen_x", label: "Gen X (42-57)" },
  { value: "baby_boomers", label: "Baby Boomers (58-76)" },
  { value: "urban_residents", label: "Urban Residents" },
  { value: "rural_residents", label: "Rural Residents" },
  { value: "suburban_residents", label: "Suburban Residents" }
];

// Sample size options
const sampleSizeOptions = [
  { value: 30, label: "30 responses" },
  { value: 50, label: "50 responses" },
  { value: 100, label: "100 responses (recommended)" },
  { value: 250, label: "250 responses" },
  { value: 500, label: "500 responses" },
];

export function SimulationSetup({
  isLoading,
  demographic,
  setDemographic,
  sampleSize,
  setSampleSize,
  specialInstructions,
  setSpecialInstructions,
  onSimulate,
  error,
  questionType
}: SimulationSetupProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-3">
      {/* Left Column: Configuration */}
      <Card className="md:col-span-1">
        <CardContent className="pt-6 space-y-6">
          <div className="space-y-2">
            <label className="text-sm font-medium">Target Demographic</label>
            <Select
              value={demographic}
              onValueChange={setDemographic}
              disabled={isLoading}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select demographic group" />
              </SelectTrigger>
              <SelectContent>
                {demographicOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground mt-1">
              Select the demographic group you want to simulate responses from
            </p>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Sample Size</label>
            <Select
              value={sampleSize.toString()}
              onValueChange={(value) => setSampleSize(Number(value))}
              disabled={isLoading}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select sample size" />
              </SelectTrigger>
              <SelectContent>
                {sampleSizeOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value.toString()}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground mt-1">
              Larger samples provide more accurate results but take longer to generate
            </p>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Special Instructions</label>
            <textarea
              className="w-full p-2 border rounded-md min-h-[80px] bg-background text-sm"
              placeholder="Add any special instructions for how the survey should be run..."
              value={specialInstructions}
              onChange={(e) => setSpecialInstructions(e.target.value)}
              disabled={isLoading}
            />
            <p className="text-xs text-muted-foreground mt-1">
              Optional: Provide specific instructions for how the AI should run this simulation
            </p>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Button
            className="w-full mt-4 gap-2"
            onClick={onSimulate}
            disabled={isLoading || !demographic || !sampleSize}
          >
            {isLoading ? (
              <>
                <RefreshCcw className="h-4 w-4 animate-spin" />
                Simulating...
              </>
            ) : (
              <>
                <Brain className="h-4 w-4" />
                Run Simulation
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Right Column: Info & Preview */}
      <Card className="md:col-span-2 bg-muted/30">
        <CardContent className="pt-6 space-y-6">
          <div className="space-y-3">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Brain className="h-5 w-5" />
              About AI Simulation
            </h3>

            <p className="text-sm">
              This feature uses advanced AI to generate realistic poll responses based on demographic research
              and real-world data. The simulation provides:
            </p>

            <ul className="list-disc pl-5 text-sm space-y-1">
              <li>Realistic response distributions based on demographic patterns</li>
              <li>Data-backed analysis of response trends</li>
              <li>Academic citations that inform the simulation</li>
              <li>Confidence scores for statistical reliability</li>
              {questionType === "open_ended" && (
                <li>Generated text responses that reflect authentic language patterns</li>
              )}
            </ul>

            {questionType === "open_ended" && (
              <Alert className="mt-4">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  For open-ended questions, the simulation will generate realistic text responses rather
                  than statistical distributions. This may take slightly longer to process.
                </AlertDescription>
              </Alert>
            )}
          </div>

          <div className="space-y-3 pt-4 border-t">
            <h4 className="font-medium text-sm">How it works:</h4>
            <ol className="list-decimal pl-5 text-sm space-y-1">
              <li>Select your target demographic group</li>
              <li>Choose your desired sample size</li>
              <li>Click &quot;Run Simulation&quot; to generate results</li>
              <li>Review the distribution and analysis</li>
              <li>Navigate to other questions to simulate them as well</li>
            </ol>
            <p className="text-xs text-muted-foreground mt-2">
              Simulation results are generated based on research and statistical models,
              providing an accurate approximation of real-world responses.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
