"use client";

import { useState, useEffect } from "react";
import { useParams, useSearchParams } from "next/navigation";
import { Poll, PollQuestion, QuestionType } from "@/lib/services/polls";
import Link from "next/link";
import { usePoll, useUpdate<PERSON>oll, useDeletePoll } from "@/hooks/use-poll";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";
import {
  ArrowLeft,
  CirclePlus,
  Copy,
  GripVertical,
  RefreshCw,
  Save,
  Share2,
  Trash2,
  X,
  Brain,
  TrendingUp,
  Users,
} from "lucide-react";
import { toast } from "sonner";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { QRCodeSVG } from 'qrcode.react';
import { SimulationRequest, SimulationResponse } from "@/lib/types/simulation";
import { SimulationResults } from "@/components/simulation/simulation-results";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

export default function PollEditPage() {
  // Use the useParams hook to get the id parameter
  const params = useParams();
  const searchParams = useSearchParams();
  const pollId = params.id as string;

  // Use React Query hooks for data fetching and mutations
  const { data: fetchedPoll, isLoading, error } = usePoll(pollId);
  const { mutate: updatePollMutation } = useUpdatePoll();
  const { mutate: deletePollMutation } = useDeletePoll();
  
  // Local state for UI
  const [poll, setPoll] = useState<Poll | null>(null);
  const [showShareDialog, setShowShareDialog] = useState(false);
  const [showSimulationDialog, setShowSimulationDialog] = useState(false);
  const [simulationLoading, setSimulationLoading] = useState(false);
  const [simulationResults, setSimulationResults] = useState<SimulationResponse | null>(null);
  const [selectedDemographic, setSelectedDemographic] = useState("college_students");
  const [sampleSize, setSampleSize] = useState(100);
  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);

  // Initialize local poll state with fetched data from React Query
  useEffect(() => {
    if (fetchedPoll) {
      setPoll(fetchedPoll);
      
      // Check URL parameters
      const isNewPoll = searchParams.get('newPoll') === 'true';
      
      // Automatically open share dialog for new polls
      if (isNewPoll) {
        setShowShareDialog(true);
        // Update URL to remove the newPoll parameter (without page reload)
        const newUrl = window.location.pathname;
        window.history.replaceState({}, '', newUrl);
      }

    }
  }, [fetchedPoll, searchParams]);

  // Handle React Query error states
  useEffect(() => {
    if (error) {
      console.error("Error fetching poll:", error);
      toast.error("Failed to load poll data");
    }
  }, [error]);

  // Handle reordering of questions via drag and drop
  const handleDragEnd = (result: { destination?: { index: number }; source: { index: number } }) => {
    if (!result.destination || !poll) return;

    const items = Array.from(poll.questions);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Update order property on each question
    const updatedQuestions = items.map((item, index) => ({
      ...item,
      order: index + 1
    }));

    setPoll({
      ...poll,
      questions: updatedQuestions
    });

    toast.success("Question order updated");
  };

  // Update question text
  const updateQuestionText = (questionId: string, newText: string) => {
    if (!poll) return;

    const updatedQuestions = poll.questions.map(question =>
      question.id === questionId
        ? { ...question, text: newText }
        : question
    );

    setPoll({
      ...poll,
      questions: updatedQuestions
    });
  };

  // Toggle question required status
  const toggleRequired = (questionId: string) => {
    if (!poll) return;

    const updatedQuestions = poll.questions.map(question =>
      question.id === questionId
        ? { ...question, required: !question.required }
        : question
    );

    setPoll({
      ...poll,
      questions: updatedQuestions
    });
  };

  // Update question type
  const updateQuestionType = (questionId: string, newType: QuestionType) => {
    if (!poll) return;

    const updatedQuestions = poll.questions.map(question => {
      if (question.id !== questionId) return question;

      // If changing to an option-based type from open text
      if (newType !== 'open' && question.type === 'open') {
        return {
          ...question,
          type: newType,
          options: [
            { id: `o-${Date.now()}-1`, text: "Option 1", value: "option_1" },
            { id: `o-${Date.now()}-2`, text: "Option 2", value: "option_2" }
          ]
        };
      }

      // If changing to open text, remove options
      if (newType === 'open' && question.type !== 'open') {
        // Use object destructuring without saving options
        const { ...questionWithoutOptions } = question;
        delete questionWithoutOptions.options;
        return {
          ...questionWithoutOptions,
          type: newType
        };
      }

      // All other cases
      return {
        ...question,
        type: newType
      };
    });

    setPoll({
      ...poll,
      questions: updatedQuestions
    });
  };

  // Handle updating an option
  const updateOption = (questionId: string, optionId: string, newText: string) => {
    if (!poll) return;

    const updatedQuestions = poll.questions.map(question => {
      if (question.id !== questionId) return question;

      const updatedOptions = question.options?.map(option =>
        option.id === optionId
          ? {
              ...option,
              text: newText,
              value: newText.toLowerCase().replace(/\s+/g, '_')
            }
          : option
      );

      return {
        ...question,
        options: updatedOptions
      };
    });

    setPoll({
      ...poll,
      questions: updatedQuestions
    });
  };

  // Add a new option to a question
  const addOption = (questionId: string) => {
    if (!poll) return;

    const updatedQuestions = poll.questions.map(question => {
      if (question.id !== questionId) return question;

      const optionId = `o-${Date.now()}`;
      const newOption = {
        id: optionId,
        text: `Option ${(question.options?.length || 0) + 1}`,
        value: `option_${(question.options?.length || 0) + 1}`
      };

      return {
        ...question,
        options: [...(question.options || []), newOption]
      };
    });

    setPoll({
      ...poll,
      questions: updatedQuestions
    });
  };

  // Remove an option from a question
  const removeOption = (questionId: string, optionId: string) => {
    if (!poll) return;

    const question = poll.questions.find(q => q.id === questionId);

    // Don't remove if it's the last option
    if (question?.options && question.options.length <= 2) {
      toast.error("Questions must have at least 2 options");
      return;
    }

    const updatedQuestions = poll.questions.map(question => {
      if (question.id !== questionId) return question;

      return {
        ...question,
        options: question.options?.filter(option => option.id !== optionId)
      };
    });

    setPoll({
      ...poll,
      questions: updatedQuestions
    });
  };

  // Add a new question to the poll
  const addQuestion = () => {
    if (!poll) return;

    const newQuestion: PollQuestion = {
      id: `q-${Date.now()}`,
      text: "New Question",
      type: "single",
      options: [
        { id: `o-${Date.now()}-1`, text: "Option 1", value: "option_1" },
        { id: `o-${Date.now()}-2`, text: "Option 2", value: "option_2" }
      ],
      required: false,
      order: poll.questions.length + 1
    };

    setPoll({
      ...poll,
      questions: [...poll.questions, newQuestion]
    });
  };

  // Duplicate a question
  const duplicateQuestion = (questionId: string) => {
    if (!poll) return;

    const questionToDuplicate = poll.questions.find(q => q.id === questionId);

    if (!questionToDuplicate) return;

    const timestamp = Date.now();
    const duplicatedQuestion: PollQuestion = {
      ...questionToDuplicate,
      id: `q-${timestamp}`,
      text: `${questionToDuplicate.text} (Copy)`,
      order: poll.questions.length + 1,
      options: questionToDuplicate.options?.map(option => ({
        ...option,
        id: `o-${timestamp}-${option.id}`
      }))
    };

    setPoll({
      ...poll,
      questions: [...poll.questions, duplicatedQuestion]
    });

    toast.success("Question duplicated");
  };

  // Remove a question from the poll
  const removeQuestion = (questionId: string) => {
    if (!poll) return;

    // Only allow removing if there's more than one question
    if (poll.questions.length <= 1) {
      toast.error("Polls must have at least one question");
      return;
    }

    const updatedQuestions = poll.questions
      .filter(question => question.id !== questionId)
      .map((question, index) => ({
        ...question,
        order: index + 1
      }));

    setPoll({
      ...poll,
      questions: updatedQuestions
    });
    
    toast.success("Question removed");
  };


// Save changes to the poll using React Query mutation
const savePoll = () => {
  if (!poll) return;

  // Show loading toast to indicate saving is in progress
  const loadingId = toast.loading("Saving poll...");

  try {
    // Simplify questions for update by removing any derived or unnecessary fields
    const simplifiedQuestions = poll.questions.map((q) => ({
      ...q,
      // Make sure each question is associated with this poll
      poll_id: pollId,
    }));

    // Create the update object with the simplified questions
    const updatedPoll = {
      ...poll,
      questions: simplifiedQuestions,
    };

    // Use React Query mutation to update the poll
    updatePollMutation(
      { id: poll.id, updates: updatedPoll },
      {
        onSuccess: () => {
          toast.success("Poll saved successfully", {
            id: loadingId,
          });
        },
        onError: (error) => {
          console.error("Error saving poll:", error);
          toast.error("Failed to save poll", {
            id: loadingId,
          });
        }
      }
    );
  } catch (error) {
    console.error("Error preparing poll update:", error);
    toast.error("Failed to save poll", {
      id: loadingId,
    });
  }
};

// Update poll metadata
const updatePollMetadata = (field: keyof Poll, value: string | boolean) => {
  if (!poll) return;

  setPoll({
    ...poll,
    [field]: value
  });
};

// Get public poll URL
const getPublicPollUrl = (includePreview = false) => {
  // Handle SSR case where window might not be available
  const origin = typeof window !== 'undefined' ? window.location.origin : 'https://pollgpt.com';
  const baseUrl = `${origin}/poll/${pollId}`;
  return includePreview ? `${baseUrl}?preview=true` : baseUrl;
};

  // Handle poll simulation
const handleSimulation = async () => {
  if (!poll) return;
  
  // If a specific question is selected, do a question-specific simulation
  if (poll.questions[selectedQuestionIndex]) {
    const question = poll.questions[selectedQuestionIndex];
    
    if (question.type === 'open') {
      toast.error("Simulation is not available for open text questions");
      return;
    }
    
    setSimulationLoading(true);
    setSimulationResults(null);
    
    try {
      const options = question.options?.map(option => option.text) || [];
      
      const request: SimulationRequest = {
        pollQuestion: question.text,
        pollOptions: options,
        demographic: {
          group: selectedDemographic.replace('_', ' '),
          size: sampleSize,
          context: `Simulating responses for a poll about: ${poll.title}`
        },
        responseFormat: 'distribution'
      };
      
      const response = await fetch('/api/simulate-poll', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Simulation failed');
      }
      
      const apiResult = await response.json();
      setSimulationResults(apiResult.simulation);
      toast.success("Simulation completed successfully");
    } catch (error) {
      console.error("Simulation error:", error);
      toast.error(error instanceof Error ? error.message : "Failed to simulate poll");
    } finally {
      setSimulationLoading(false);
    }
  } else {
    // Full poll simulation (backup implementation)
    setSimulationLoading(true);
    
    try {
      // Create a simulation request for the full poll
      const request: SimulationRequest = {
        pollQuestion: poll.title,
        pollOptions: [], // No options for full poll simulation
        demographic: {
          group: selectedDemographic.replace('_', ' '),
          size: sampleSize,
          context: `Full poll simulation for: ${poll.title}`
        },
        responseFormat: 'distribution' // Must be one of the allowed values
      };
      
      // Make API call
      const response = await fetch('/api/simulate-poll', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate simulation');
      }
      
      const data = await response.json();
      setSimulationResults(data.simulation);
      toast.success("Full poll simulation completed");
    } catch (error) {
      console.error('Error in simulation:', error);
      toast.error('Failed to generate simulation');
    } finally {
      setSimulationLoading(false);
    }
  }
};

// Handle poll deletion
const handleDeletePoll = () => {
  if (!poll) return;
  
  const confirmDelete = window.confirm("Are you sure you want to delete this poll? This cannot be undone.");
  
  if (confirmDelete) {
    toast.loading("Deleting poll...");
    
    deletePollMutation(
      { id: poll.id },
      {
        onSuccess: () => {
          toast.success("Poll deleted successfully");
          // Navigate back to polls list
          window.location.href = "/dashboard/polls";
        },
        onError: (error) => {
          console.error("Error deleting poll:", error);
          toast.error("Failed to delete poll");
        }
      }
    );
  }
};

// Show loading state while fetching data, regardless of poll state
if (isLoading || !poll) {
  return (
    <div className="flex items-center justify-center min-h-[60vh]">
      <div className="space-y-4 w-full max-w-2xl">
        <div className="space-y-2">
          <div className="flex items-center">
            <Skeleton className="h-4 w-16 mr-2" />
            <Skeleton className="h-8 w-64" />
          </div>
          <Skeleton className="h-12 w-full" />
        </div>

        <div className="space-y-2 pt-6">
          <div className="flex items-center">
            <Skeleton className="h-4 w-24 mr-2" />
          </div>

          {[1, 2, 3].map((i) => (
            <div key={i} className="border rounded-lg p-4 space-y-4">
              <div className="space-x-2">
                <Button className="mr-4" onClick={savePoll}>
                  <Save className="mr-2 h-4 w-4" /> Save Changes
                </Button>
                <Button variant="ghost" onClick={handleDeletePoll}>
                  <Trash2 className="mr-2 h-4 w-4" /> Delete
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

  // Handle poll deletion
  const handleDeletePoll = () => {
    if (!poll) return;
    
    const confirmDelete = window.confirm("Are you sure you want to delete this poll? This cannot be undone.");
    
    if (confirmDelete) {
      toast.loading("Deleting poll...");
      
      deletePollMutation(
        { id: poll.id },
        {
          onSuccess: () => {
            toast.success("Poll deleted successfully");
            // Navigate back to polls list
            window.location.href = "/dashboard/polls";
          },
          onError: (error) => {
            console.error("Error deleting poll:", error);
            toast.error("Failed to delete poll");
          }
        }
      );
    }
      });
      
      if (!response.ok) {
        throw new Error('Simulation failed');
      }
      
      const data = await response.json();
      setSimulationResults(data);
      
      // Set the first question as selected by default
      setSelectedQuestionIndex(0);
      
      toast.success('Simulation completed successfully');
    } catch (error) {
      console.error('Error in simulation:', error);
      toast.error('Failed to generate simulation');
    } finally {
      setSimulationLoading(false);
    }
  };

  return (
    <div className="space-y-8 pb-16">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <Button asChild variant="outline" size="sm" className="gap-1 w-fit">
          <Link href="/dashboard/polls">
            <ArrowLeft className="h-4 w-4" />
            Back to Polls
          </Link>
        </Button>
        <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-2">
          <Button
            asChild
            variant="outline"
            size="sm"
            className="gap-1"
          >
            <Link href={`/dashboard/polls/${pollId}/simulate`}>
              <Brain className="h-4 w-4" />
              Full Simulation
            </Link>
          </Button>
          <Button
            onClick={() => setShowShareDialog(true)}
            variant="outline"
            size="sm"
            className="gap-1"
          >
            <Share2 className="h-4 w-4" />
            Share
          </Button>
          <Button onClick={savePoll} className="gap-1">
            <Save className="h-4 w-4" />
            Save Changes
          </Button>
        </div>
      </div>

      {/* Poll Metadata */}
      <Card>
        <CardHeader className="pb-4">
          <div className="space-y-1">
            <Label htmlFor="poll-title">Poll Title</Label>
            <Textarea
              id="poll-title"
              value={poll.title}
              onChange={(e) => updatePollMetadata('title', e.target.value)}
              placeholder="Enter poll title"
              className="text-lg font-medium resize-none min-h-[60px]"
              rows={2}
            />
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-1">
            <Label htmlFor="poll-description">Description</Label>
            <Textarea
              id="poll-description"
              value={poll.description}
              onChange={(e) => updatePollMetadata('description', e.target.value)}
              placeholder="Enter a description for your poll"
              rows={3}
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-1">
              <Label htmlFor="poll-status">Status</Label>
              <Select
                value={poll.status}
                onValueChange={(value) => updatePollMetadata('status', value as 'draft' | 'active' | 'completed')}
              >
                <SelectTrigger id="poll-status">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-1">
              <Label htmlFor="poll-expires">Expiration Date</Label>
              <Input
                id="poll-expires"
                type="date"
                value={poll.expiresAt ? poll.expiresAt.split('T')[0] : ''}
                onChange={(e) => updatePollMetadata('expiresAt', e.target.value)}
              />
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="poll-visibility" className="text-base">Poll Visibility</Label>
                <Switch
                  id="poll-visibility"
                  checked={poll.is_public}
                  onCheckedChange={(checked) => updatePollMetadata('is_public', checked)}
                />
              </div>
              <div className="text-sm text-muted-foreground">
                {poll.is_public ? 'Public - Anyone with the link can access' : 'Private - Requires access code'}
              </div>

              {!poll.is_public && (
                <div className="space-y-1 mt-2">
                  <Label htmlFor="access-code">Access Code</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="access-code"
                      placeholder="Set an access code"
                      value={poll.access_code || ''}
                      onChange={(e) => updatePollMetadata('access_code', e.target.value)}
                    />
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => {
                        const code = Math.random().toString(36).substring(2, 8).toUpperCase();
                        updatePollMetadata('access_code', code);
                      }}
                      title="Generate random code"
                    >
                      <RefreshCw className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Questions */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-semibold">Questions</h3>
          <Button onClick={addQuestion} variant="outline" className="gap-1">
            <CirclePlus className="h-4 w-4" />
            Add Question
          </Button>
        </div>

        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="questions">
            {(provided) => (
              <div
                {...provided.droppableProps}
                ref={provided.innerRef}
                className="space-y-4"
              >
                {poll.questions.map((question, index) => (
                  <Draggable
                    key={question.id}
                    draggableId={question.id}
                    index={index}
                  >
                    {(provided) => (
                      <Card
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        className="border-2"
                      >
                        <CardHeader className="pb-2">
                          {/* Action buttons at the top */}
                          <div className="flex items-center justify-between mb-4">
                            <div
                              {...provided.dragHandleProps}
                              className="cursor-move p-1 hover:bg-muted rounded"
                            >
                              <GripVertical className="h-5 w-5 text-muted-foreground" />
                            </div>
                            <div className="flex gap-1">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => duplicateQuestion(question.id)}
                                title="Duplicate question"
                              >
                                <Copy className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => removeQuestion(question.id)}
                                title="Delete question"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>

                          {/* Question content below the buttons */}
                          <div className="space-y-3">
                            <Textarea
                              value={question.text}
                              onChange={(e) => updateQuestionText(question.id, e.target.value)}
                              placeholder="Enter question text"
                              className="text-lg font-medium resize-none min-h-[80px] w-full"
                              rows={2}
                            />
                            <div className="flex flex-col gap-2 sm:flex-row sm:flex-wrap sm:gap-2">
                              <Select
                                value={question.type}
                                onValueChange={(value) => updateQuestionType(question.id, value as QuestionType)}
                              >
                                <SelectTrigger className="w-full sm:w-[140px]">
                                  <SelectValue placeholder="Question type" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="single">Single Choice</SelectItem>
                                  <SelectItem value="multiple">Multiple Choice</SelectItem>
                                  <SelectItem value="likert">Likert Scale</SelectItem>
                                  <SelectItem value="open">Open Text</SelectItem>
                                </SelectContent>
                              </Select>
                              <div className="flex items-center space-x-2">
                                <Checkbox
                                  id={`required-${question.id}`}
                                  checked={question.required}
                                  onCheckedChange={() => toggleRequired(question.id)}
                                />
                                <label
                                  htmlFor={`required-${question.id}`}
                                  className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                >
                                  Required
                                </label>
                              </div>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          {question.type === 'open' ? (
                            <div className="bg-muted p-3 rounded-md">
                              <p className="text-muted-foreground text-sm italic">
                                Open text response field
                              </p>
                            </div>
                          ) : (
                            <div className="space-y-3">
                              {question.options?.map((option) => (
                                <div key={option.id} className="flex items-center gap-2">
                                  {question.type === 'single' || question.type === 'likert' ? (
                                    <RadioGroup>
                                      <div className="flex items-center space-x-2">
                                        <RadioGroupItem value={option.value} id={option.id} />
                                      </div>
                                    </RadioGroup>
                                  ) : (
                                    <Checkbox id={option.id} />
                                  )}
                                  <Input
                                    value={option.text}
                                    onChange={(e) => updateOption(question.id, option.id, e.target.value)}
                                    className="flex-1"
                                  />
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => removeOption(question.id, option.id)}
                                    className="h-8 w-8"
                                    title="Remove option"
                                  >
                                    <X className="h-4 w-4" />
                                  </Button>
                                </div>
                              ))}
                              <Button
                                variant="outline"
                                onClick={() => addOption(question.id)}
                                className="text-sm mt-2"
                              >
                                Add Option
                              </Button>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      </div>

      {/* Share Dialog */}
      <Dialog open={showShareDialog} onOpenChange={setShowShareDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Share Poll</DialogTitle>
            <DialogDescription>
              Share your poll with others using these options.
            </DialogDescription>
          </DialogHeader>

          {(!poll.is_public || poll.status !== 'active') && (
            <div className="bg-yellow-100 dark:bg-yellow-900 p-3 rounded-md mb-4">
              <p className="text-yellow-800 dark:text-yellow-200 text-sm font-medium">Sharing Warning:</p>
              <ul className="text-yellow-800 dark:text-yellow-200 text-sm list-disc pl-5 mt-1">
                {!poll.is_public && <li>This poll is set to private. Users will need the access code to view it.</li>}
                {poll.status !== 'active' && <li>This poll is not active. Users won&apos;t be able to access it until it&apos;s activated.</li>}
              </ul>
            </div>
          )}

          <Tabs defaultValue="link">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="link">Link</TabsTrigger>
              <TabsTrigger value="qrcode">QR Code</TabsTrigger>
              <TabsTrigger value="embed">Embed</TabsTrigger>
            </TabsList>

            {/* Link Tab */}
            <TabsContent value="link" className="space-y-4">
              <div className="flex space-x-2">
                <Input
                  value={getPublicPollUrl()}
                  readOnly
                  className="flex-1"
                />
                <Button
                  size="icon"
                  onClick={() => {
                    navigator.clipboard.writeText(getPublicPollUrl());
                    toast.success("Link copied to clipboard");
                  }}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </TabsContent>

            {/* QR Code Tab */}
            <TabsContent value="qrcode" className="space-y-4">
              <div className="flex justify-center py-4">
                <div className="bg-white p-4 rounded-lg">
                  <QRCodeSVG value={getPublicPollUrl()} size={200} />
                </div>
              </div>
              <div className="flex justify-center">
                <Button
                  onClick={() => {
                    const svg = document.querySelector('.react-qr-svg') as SVGElement;
                    if (svg) {
                      const svgData = new XMLSerializer().serializeToString(svg);
                      const canvas = document.createElement('canvas');
                      const ctx = canvas.getContext('2d');
                      const img = new Image();
                      img.onload = () => {
                        canvas.width = img.width;
                        canvas.height = img.height;
                        ctx?.drawImage(img, 0, 0);
                        const pngFile = canvas.toDataURL('image/png');
                        const downloadLink = document.createElement('a');
                        downloadLink.download = `${poll.title.replace(/\s+/g, '-')}-qrcode.png`;
                        downloadLink.href = pngFile;
                        downloadLink.click();
                      };
                      img.src = 'data:image/svg+xml;base64,' + btoa(svgData);
                    }
                  }}
                  variant="outline"
                  className="gap-1"
                >
                  <Save className="h-4 w-4" />
                  Download QR Code
                </Button>
              </div>
            </TabsContent>

            {/* Embed Tab */}
            <TabsContent value="embed" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="embed-code">Embed Code</Label>
                <Textarea
                  id="embed-code"
                  readOnly
                  rows={4}
                  value={`<iframe src="${getPublicPollUrl()}" width="100%" height="600" frameborder="0"></iframe>`}
                />
                <Button
                  onClick={() => {
                    navigator.clipboard.writeText(`<iframe src="${getPublicPollUrl()}" width="100%" height="600" frameborder="0"></iframe>`);
                    toast.success("Embed code copied to clipboard");
                  }}
                  variant="outline"
                  className="w-full gap-1"
                >
                  <Copy className="h-4 w-4" />
                  Copy Embed Code
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>

      {/* Simulation Dialog */}
      <Dialog open={showSimulationDialog} onOpenChange={setShowSimulationDialog}>
        <DialogContent className="max-w-2xl w-full max-h-[95vh] overflow-y-auto flex flex-col p-0">
          {/* Simulation Form Card */}
          <div className="w-full max-w-xl mx-auto mt-6 mb-4">
            <div className="bg-white dark:bg-muted rounded-xl shadow-lg border p-6 space-y-6 animate-fade-in">
              <h3 className="text-lg font-semibold flex items-center gap-2 mb-2"><Brain className="h-5 w-5 text-primary" /> Simulation Setup</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-1">
                  <Label className="flex items-center gap-2"><span className="text-primary"><TrendingUp className="h-4 w-4" /></span> Question</Label>
                  <Select value={selectedQuestionIndex.toString()} onValueChange={(value) => setSelectedQuestionIndex(parseInt(value))}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Choose a question" />
                    </SelectTrigger>
                    <SelectContent>
                      {poll.questions.map((question, index) => (
                        <SelectItem
                          key={question.id}
                          value={index.toString()}
                          disabled={question.type === 'open'}
                        >
                          {question.text} {question.type === 'open' && '(Not available for open text)'}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-1">
                  <Label className="flex items-center gap-2"><span className="text-primary"><Users className="h-4 w-4" /></span> Demographic</Label>
                  <Select value={selectedDemographic} onValueChange={setSelectedDemographic}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Choose demographic" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="college_students">College Students</SelectItem>
                      <SelectItem value="working_professionals">Working Professionals</SelectItem>
                      <SelectItem value="high_school_students">High School Students</SelectItem>
                      <SelectItem value="parents">Parents</SelectItem>
                      <SelectItem value="seniors">Seniors (65+)</SelectItem>
                      <SelectItem value="millennials">Millennials</SelectItem>
                      <SelectItem value="gen_z">Gen Z</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-1 md:col-span-2">
                  <Label className="flex items-center gap-2"><span className="text-primary"><Users className="h-4 w-4" /></span> Sample Size</Label>
                  <Select value={sampleSize.toString()} onValueChange={(value) => setSampleSize(parseInt(value))}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Choose sample size" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="50">50 responses</SelectItem>
                      <SelectItem value="100">100 responses</SelectItem>
                      <SelectItem value="250">250 responses</SelectItem>
                      <SelectItem value="500">500 responses</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>

          {/* Results Card */}
          {simulationResults && (
            <div className="w-full max-w-xl mx-auto mb-6 animate-fade-in">
              <div className="bg-white dark:bg-muted rounded-xl shadow-lg border p-6 overflow-y-auto max-h-[60vh] relative">
                <SimulationResults
                  results={simulationResults}
                  className="mt-0"
                  options={poll.questions[selectedQuestionIndex]?.options?.map(opt => opt.text) || []}
                  enhancedUI
                />
              </div>
            </div>
          )}

          {/* Sticky Footer Button */}
          <div className="sticky bottom-0 z-10 bg-background px-6 py-4 border-t flex flex-col gap-2">
            <Button
              onClick={handleSimulation}
              className="w-full text-base py-3"
              disabled={simulationLoading}
            >
              {simulationLoading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Generating Simulation...
                </>
              ) : (
                <>
                  <Brain className="h-4 w-4 mr-2" />
                  Generate Simulation
                </>
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
