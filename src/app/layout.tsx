import type { <PERSON>ada<PERSON>, Viewport } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

import { ToastProvider } from "@/components/providers/toast-provider";
import { ThemeProvider } from "@/components/providers/theme-provider";
import { AuthProvider } from "@/components/providers/auth-provider";
import { Analytics } from "@vercel/analytics/react";
import { AuthDebugger } from "@/components/ui/auth-debugger";
import QueryProvider from "@/providers/query-provider";

// Check for environment variables at build/runtime
if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
  console.warn("WARNING: Supabase environment variables are missing. Authentication will not work correctly.");
}

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = <PERSON>eist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
};

export const metadata: Metadata = {
  metadataBase: new URL('https://pollgpt.com'),
  title: "PollGPT | #1 AI-Powered Poll & Survey Creator",
  description: "PollGPT - Create intelligent polls with AI in seconds. The best way to build surveys, collect responses, and gain actionable insights with minimal effort.",
  keywords: ["PollGPT", "Poll GPT", "AI polling", "survey platform", "intelligent polls", "AI survey creator", "GPT polls", "free poll maker", "AI polls", "GPT survey", "poll app"],
  robots: {
    index: true,
    follow: true,
    nocache: true,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
    },
  },
  icons: {
    icon: [
      { url: "/favicon.svg", type: "image/svg+xml" },
      { url: "/favicon.ico" },
      { url: "/favicon-96x96.png", sizes: "96x96", type: "image/png" }
    ],
    apple: [
      { url: "/apple-touch-icon.png", sizes: "180x180", type: "image/png" }
    ]
  },
  authors: [{ name: "PollGPT Team", url: "https://pollgpt.com" }],
  creator: "PollGPT",
  publisher: "PollGPT",
  applicationName: "PollGPT",
  category: "Technology",
  openGraph: {
    title: "PollGPT - AI-Powered Polling Platform",
    description: "Create intelligent polls with AI assistance. Collect responses, and gain actionable insights with minimal effort.",
    url: "https://pollgpt.com",
    siteName: "PollGPT",
    type: "website",
    locale: "en_US",
    images: [{
      url: "/web-app-manifest-512x512.png",
      width: 512,
      height: 512,
      alt: "PollGPT - AI-Powered Polling Platform"
    }]
  },
  twitter: {
    card: "summary_large_image",
    title: "PollGPT - AI-Powered Polling Platform",
    description: "Create intelligent polls with AI assistance. Collect responses, and gain actionable insights with minimal effort.",
    images: [{ url: "/web-app-manifest-512x512.png" }],
    creator: "@pollgpt",
    site: "https://x.com/pollgpt"
  },
  alternates: {
    canonical: "https://pollgpt.com",
    languages: {
      'en-US': 'https://pollgpt.com',
    },
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="preload" href="/schema.jsonld" as="fetch" crossOrigin="anonymous" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "PollGPT",
              "alternateName": "Poll GPT",
              "url": "https://pollgpt.com",
              "logo": {
                "@type": "ImageObject",
                "url": "https://pollgpt.com/pollgpt-icon.svg",
                "width": "512",
                "height": "512"
              },
              "description": "AI-Powered Polling Platform for creating and analyzing polls with AI assistance",
              "keywords": "poll gpt, pollgpt, ai polls, survey platform, ai survey",
              "address": {
                "@type": "PostalAddress",
                "streetAddress": "5 Parv. Alan Turing",
                "addressLocality": "Paris",
                "postalCode": "75013",
                "addressCountry": "France"
              },
              "contactPoint": {
                "@type": "ContactPoint",
                "contactType": "Customer Support",
                "email": "<EMAIL>"
              },
              "sameAs": [
                "https://x.com/pollgpt",
                "https://linkedin.com/company/pollgpt"
              ]
            })
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <AuthProvider>
            <QueryProvider>
              <ToastProvider />
              {children}
              {process.env.NODE_ENV === 'development' && <AuthDebugger />}
              <Analytics />
            </QueryProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
