import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type { Poll } from '@/lib/types/poll';
import type { PollOption } from '@/lib/types/poll';
import { pollKeys } from '../lib/api/query-keys';
import type { PollFilters } from '../lib/api/query-keys';
import { useSessionRefresh } from '@/lib/hooks/useSessionRefresh';
import { invalidatePollLists } from '@/lib/utils/cache-utils';
import { supabase } from '@/lib/supabase';

// Re-export PollFilters and pollKeys for backward compatibility
export { pollKeys };
export type { PollFilters };

// Interface for paginated polls response
export interface PaginatedPolls {
  data: Poll[];
  totalCount: number;
  page: number;
  pageSize: number;
}

// Function to fetch polls with pagination and filtering
export async function fetchPolls({
  page = 1,
  pageSize = 10,
  searchQuery = '',
  statusFilter = null,
  userId,
}: PollFilters): Promise<PaginatedPolls> {
  try {
    // Get the current user session
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      throw new Error('Not authenticated');
    }

    // Use the user ID from session if not explicitly provided
    const effectiveUserId = userId || session.user.id;

    // Use the optimized RPC function to fetch polls with server-side filtering
    console.time('fetch-polls-rpc');
    const { data, error } = await supabase.rpc('get_polls_with_counts', {
      user_id_param: effectiveUserId,
      page_number: page,
      page_size: pageSize,
      search_query_param: searchQuery || '',
      status_filter_param: statusFilter || null,
      fetch_all: false
    });

    console.timeEnd('fetch-polls-rpc'); // Log how long the fetch took

    if (error) {
      console.error('Error fetching polls:', error);
      throw new Error(error.message);
    }

    if (!data) {
      return {
        data: [],
        totalCount: 0,
        page,
        pageSize,
      };
    }

    // Extract the total count from the response
    const totalCount = data && data.length > 0 ? data[0].total_count || 0 : 0;

    // Transform the data to match the Poll interface
    const transformedData: Poll[] = (data || []).map((poll: Record<string, unknown>) => ({
      id: poll.id as string,
      title: poll.title as string,
      slug: (poll.slug as string) || `poll-${poll.id}`,
      description: (poll.description as string) || '',
      createdAt: poll.created_at as string,
      updatedAt: poll.updated_at as string,
      expiresAt: poll.expires_at as string | null,
      userId: poll.user_id as string,
      questions: Array.isArray(poll.questions) ? poll.questions.map((q: Record<string, unknown>) => ({
        id: q.id as string,
        text: q.question_text as string,
        type: (q.question_type as string || 'single') as 'single' | 'multiple' | 'likert' | 'open',
        options: q.options as PollOption[] || [],
        required: q.required as boolean || false,
        order: q.order as number || 0
      })) : [],
      questions_count: Array.isArray(poll.questions) ? poll.questions.length : 0,
      responses_count: Number(poll.response_count) || 0,
      views_count: Number(poll.view_count) || 0,
      status: (poll.status as string) || 'draft',
      is_public: Boolean(poll.is_public)
    }));

    return {
      data: transformedData,
      totalCount,
      page,
      pageSize,
    };
  } catch (error) {
    console.error('Error in fetchPolls:', error);
    throw error;
  }
}

// Hook for fetching polls with React Query
export function usePolls(filters: PollFilters = {}) {
  const { refreshSession } = useSessionRefresh();

  return useQuery<PaginatedPolls, Error>({
    queryKey: pollKeys.list(filters),
    queryFn: async () => {
      try {
        console.time('fetch-polls');
        const result = await fetchPolls(filters);
        console.timeEnd('fetch-polls');
        return result;
      } catch (error) {
        console.error('Error fetching polls:', error);

        // If we get an authentication error, try to refresh the session
        const err = error as { message?: string; status?: number };
        if (err.message?.includes('JWT expired') ||
            err.message?.includes('not authenticated') ||
            err.status === 401) {
          console.log('Attempting session refresh due to auth error');
          const refreshed = await refreshSession();
          if (refreshed) {
            // Retry the fetch after successful refresh
            console.log('Session refreshed, retrying fetch');
            console.time('fetch-polls-after-refresh');
            const result = await fetchPolls(filters);
            console.timeEnd('fetch-polls-after-refresh');
            return result;
          }
        }
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes to match global config
    gcTime: 10 * 60 * 1000, // 10 minutes cache time
    retry: 1, // Only retry once on failure
    refetchOnWindowFocus: true, // Enable refresh when returning to the tab
    // Use previous data as placeholder while loading new data
    placeholderData: (previousData) => previousData
  });
}

// Hook for deleting polls with React Query
export function useDeletePoll() {
  const queryClient = useQueryClient();

  return useMutation<string, Error, string>({
    mutationFn: async (pollId: string) => {
      // Import the function dynamically to avoid circular dependencies
      const { deletePoll } = await import('@/lib/services/polls');
      const result = await deletePoll(pollId);
      if (!result) throw new Error('Failed to delete poll');
      return pollId;
    },

    onMutate: async (deletedPollId) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: pollKeys.lists() });

      // Snapshot the previous value for all affected queries
      const previousLists = queryClient.getQueriesData({ queryKey: pollKeys.lists() });

      // Optimistically update all lists that contain this poll
      queryClient.setQueriesData<PaginatedPolls>(
        { queryKey: pollKeys.lists() },
        (old) => {
          if (!old || typeof old !== 'object' || !('data' in old) || !Array.isArray(old.data)) return old;
          return {
            ...old,
            data: old.data.filter((poll: Poll) => poll.id !== deletedPollId),
            // If we have a total count, decrease it by 1
            totalCount: ('totalCount' in old && typeof old.totalCount === 'number') ? old.totalCount - 1 : 0
          };
        }
      );

      // Also remove the poll from any detail queries
      queryClient.removeQueries({ queryKey: pollKeys.detail(deletedPollId) });

      return { previousLists };
    },
    onError: (err, deletedPollId, context: { previousLists?: Array<[unknown[], unknown]> } | undefined) => {
      // If the mutation fails, use the context to roll back all affected queries
      if (context?.previousLists) {
        context.previousLists.forEach(([queryKey, previousData]) => {
          queryClient.setQueryData(queryKey, previousData);
        });
      }

      // Show error to user
      console.error('Failed to delete poll:', err);
    },
    onSuccess: (deletedPollId) => {
      console.log(`Successfully deleted poll ${deletedPollId}`);
    },
    onSettled: () => {
      // Only invalidate lists, not all polls (more granular)
      invalidatePollLists(queryClient);
    }
  });
}

// Function to duplicate a poll
export async function duplicatePoll(id: string): Promise<Poll | null> {
  try {
    // Import the function dynamically to avoid circular dependencies
    const { getPollById, createPoll } = await import('@/lib/services/polls');

    // First, get the poll to duplicate
    const poll = await getPollById(id);

    if (!poll) {
      throw new Error('Poll not found');
    }

    // Create a new poll with the same data but as a draft
    const newPoll = await createPoll({
      title: `${poll.title} (Copy)`,
      description: poll.description,
      questions: poll.questions,
      status: 'draft',
      is_public: poll.is_public
    });

    return newPoll;
  } catch (error) {
    console.error('Error duplicating poll:', error);
    return null;
  }
}

// Hook for duplicating polls with React Query
export function useDuplicatePoll() {
  const queryClient = useQueryClient();

  return useMutation<Poll, Error, string>({
    mutationFn: duplicatePoll,
    onSuccess: () => {
      // Invalidate and refetch polls list after duplication
      queryClient.invalidateQueries({ queryKey: pollKeys.lists() });
    },
  });
}

// Function to close a poll
export async function closePoll(id: string) {
  const { error } = await supabase
    .from('polls')
    .update({ status: 'closed' })
    .eq('id', id);

  if (error) {
    console.error('Error closing poll:', error);
    throw new Error(error.message);
  }

  return id;
}

// Hook for closing polls with React Query
export function useClosePoll() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: closePoll,
    onSuccess: (id) => {
      // Invalidate specific poll and polls list
      queryClient.invalidateQueries({ queryKey: pollKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: pollKeys.lists() });
    },
  });
}
