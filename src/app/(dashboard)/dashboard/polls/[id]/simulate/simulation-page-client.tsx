"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { createBrowserClient } from '@supabase/ssr';
import { Database } from "@/lib/database.types";
import SimulationDashboard from "@/components/simulation/simulation-dashboard";
import { useAuth } from "@/components/providers/auth-provider";
import { Skeleton } from "@/components/ui/skeleton";

interface SimulationPageClientProps {
  pollId: string;
}

type PollWithQuestions = Database["public"]["Tables"]["polls"]["Row"] & {
  questions: Database["public"]["Tables"]["questions"]["Row"][]
};

export default function SimulationPageClient({ pollId }: SimulationPageClientProps) {
  const { user, loading } = useAuth();
  const [poll, setPoll] = useState<PollWithQuestions | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const router = useRouter();
  const redirectedRef = useRef(false);

  // Handle authentication without AuthGuard
  useEffect(() => {
    // Only redirect when we're sure the user is not authenticated and we haven't redirected yet
    if (!loading && !user && !redirectedRef.current) {
      redirectedRef.current = true;
      const returnPath = `/dashboard/polls/${pollId}/simulate`;
      const returnUrl = encodeURIComponent(returnPath);
      console.log("User not authenticated, redirecting to login");
      router.push(`/login?redirect=${returnUrl}`);
    }
  }, [user, loading, pollId, router]);

  // Check for cached poll data in localStorage
  useEffect(() => {
    if (user) {
      // Try to get cached poll data first
      const cachedPollKey = `poll_${pollId}_${user.id}`;
      const cachedPoll = localStorage.getItem(cachedPollKey);
      
      if (cachedPoll) {
        try {
          const parsedPoll = JSON.parse(cachedPoll);
          const cacheTime = parsedPoll._cacheTime;
          const now = Date.now();
          
          // Use cached data if it's less than 5 minutes old
          if (cacheTime && now - cacheTime < 5 * 60 * 1000) {
            console.log("Using cached poll data");
            setPoll(parsedPoll);
            setIsLoading(false);
            return;
          }
        } catch (e) {
          console.error("Error parsing cached poll data:", e);
          // Continue to fetch fresh data if cache parsing fails
        }
      }
      
      // Fetch fresh poll data
      const fetchPoll = async () => {
        try {
          setIsLoading(true);
          const supabase = createBrowserClient<Database>(
            process.env.NEXT_PUBLIC_SUPABASE_URL!,
            process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
          );

          // First check if the poll exists and belongs to the user
          const { data: pollData, error: pollError } = await supabase
            .from("polls")
            .select(`*, questions(*)`)
            .eq("id", pollId)
            .eq("user_id", user.id);
          
          if (pollError) {
            throw pollError;
          }
          
          if (!pollData || pollData.length === 0) {
            throw new Error("Poll not found or you don't have permission to access it");
          }
          
          // Use the first poll (should be the only one)
          const data = pollData[0];
          
          // Add cache timestamp to the data
          const pollWithCacheTime = {
            ...data,
            _cacheTime: Date.now()
          };
          
          // Cache the poll data in localStorage
          localStorage.setItem(cachedPollKey, JSON.stringify(pollWithCacheTime));
          
          setPoll(pollWithCacheTime);
        } catch (err) {
          console.error("Error fetching poll:", err);
          setError(err as Error);
        } finally {
          setIsLoading(false);
        }
      };

      fetchPoll();
    }
  }, [pollId, user, router]);

  // Don't render anything if not authenticated or still loading authentication
  if (loading || !user) {
    return (
      <div className="container max-w-7xl py-6 space-y-8">
        <div className="space-y-4">
          <Skeleton className="h-8 w-1/3" />
          <Skeleton className="h-40 w-full" />
          <Skeleton className="h-40 w-full" />
        </div>
      </div>
    );
  }

  // Render content when authenticated
  return (
    <div className="container max-w-7xl py-6 space-y-8">
      {isLoading && (
        <div className="space-y-4">
          <Skeleton className="h-8 w-1/3" />
          <Skeleton className="h-40 w-full" />
          <Skeleton className="h-40 w-full" />
        </div>
      )}

        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-700 font-medium">Error loading poll data</p>
            <p className="text-red-600">{error.message}</p>
            <button
              className="mt-2 text-sm text-red-600 hover:underline"
              onClick={() => router.push('/dashboard/polls')}
            >
              Return to polls
            </button>
          </div>
        )}

        {!isLoading && !error && !poll && (
          <div className="p-4 bg-amber-50 border border-amber-200 rounded-md">
            <p className="text-amber-700 font-medium">Poll not found</p>
            <p className="text-amber-600">The requested poll was not found or you don&apos;t have access to it.</p>
            <button
              className="mt-2 text-sm text-amber-600 hover:underline"
              onClick={() => router.push('/dashboard/polls')}
            >
              Return to polls
            </button>
          </div>
        )}

        {!isLoading && poll && (
          <SimulationDashboard poll={poll} />
        )}
    </div>
  );
}
