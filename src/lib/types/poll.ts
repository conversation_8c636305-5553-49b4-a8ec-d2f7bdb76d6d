export interface ChartDataPoint {
  name: string;
  value: number;
  fill?: string;
}

export interface OpenQuestionResponse {
  id: number;
  response: string;
}

export interface ProcessedQuestion extends Omit<PollQuestion, 'options'> {
  options?: { id: string; text: string; value: string }[];
  results: ChartDataPoint[] | OpenQuestionResponse[];
  npsData?: ChartDataPoint[];
  npsScore?: number;
}

export interface ResponseByDate {
  date: string;
  count: number;
}

export interface ProcessedPollData {
  id: string;
  title: string;
  description: string;
  createdAt: string;
  completedResponses: number;
  partialResponses: number;
  totalViews: number;
  completionRate: string;
  averageTimeToComplete: string;
  questions: ProcessedQuestion[];
  responsesByDate: ResponseByDate[];
  demographics: {
    devices: ChartDataPoint[];
    regions: ChartDataPoint[];
  };
}

export interface PollResponse {
  id: string;
  pollId: string;
  responses: Record<string, string | string[]>;
  submittedAt: string;
  respondentInfo?: {
    deviceType: string;
    region: string;
    browser?: string;
  };
}

export interface PollOption {
  id: string;
  text: string;
  value: string;
}

export interface PollQuestion {
  id: string;
  text: string;
  type: 'single' | 'multiple' | 'likert' | 'open';
  options?: PollOption[];
  required?: boolean;
  order?: number;
}

export interface Poll {
  id: string;
  title: string;
  description: string;
  createdAt: string;
  questions: PollQuestion[]; // Array of question objects, might be used for other purposes
  questions_count: number;   // Number of questions
  responses_count: number;   // Number of responses
  views_count: number;       // Number of views
}
